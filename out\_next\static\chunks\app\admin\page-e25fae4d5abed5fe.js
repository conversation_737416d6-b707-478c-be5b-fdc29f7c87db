(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698,6779],{1469:(e,s,a)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var a in s)Object.defineProperty(e,a,{enumerable:!0,get:s[a]})}(s,{default:function(){return n},getImageProps:function(){return d}});let t=a(8229),l=a(8883),r=a(3063),i=t._(a(1193));function d(e){let{props:s}=(0,l.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,a]of Object.entries(s))void 0===a&&delete s[e];return{props:s}}let n=r.Image},6766:(e,s,a)=>{"use strict";a.d(s,{default:()=>l.a});var t=a(1469),l=a.n(t)},6779:(e,s,a)=>{"use strict";a.d(s,{CF:()=>c,I0:()=>h,Pn:()=>d,TK:()=>g,getWithdrawals:()=>m,hG:()=>u,lo:()=>n,nQ:()=>x,updateWithdrawalStatus:()=>N,x5:()=>o});var t=a(5317),l=a(6104),r=a(3592);let i=new Map;async function d(){let e="dashboard-stats",s=function(e){let s=i.get(e);return s&&Date.now()-s.timestamp<3e5?s.data:null}(e);if(s)return s;try{let s=new Date;s.setHours(0,0,0,0);let a=t.Dc.fromDate(s),d=await (0,t.getDocs)((0,t.collection)(l.db,r.COLLECTIONS.users)),n=d.size,o=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.users),(0,t._M)(r.FIELD_NAMES.joinedDate,">=",a)),c=(await (0,t.getDocs)(o)).size,x=0,h=0,m=0,g=0;d.forEach(e=>{var a;let t=e.data();x+=t[r.FIELD_NAMES.totalVideos]||0,h+=t[r.FIELD_NAMES.wallet]||0;let l=null==(a=t[r.FIELD_NAMES.lastVideoDate])?void 0:a.toDate();l&&l.toDateString()===s.toDateString()&&(m+=t[r.FIELD_NAMES.todayVideos]||0)});try{let e=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.transactions),(0,t._M)(r.FIELD_NAMES.type,"==","video_earning"),(0,t.AB)(1e3));(await (0,t.getDocs)(e)).forEach(e=>{var a;let t=e.data(),l=null==(a=t[r.FIELD_NAMES.date])?void 0:a.toDate();l&&l>=s&&(g+=t[r.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let u=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.withdrawals),(0,t._M)("status","==","pending")),N=(await (0,t.getDocs)(u)).size,f=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.withdrawals),(0,t._M)("date",">=",a)),j=(await (0,t.getDocs)(f)).size,y={totalUsers:n,totalVideos:x,totalEarnings:h,pendingWithdrawals:N,todayUsers:c,todayVideos:m,todayEarnings:g,todayWithdrawals:j};return i.set(e,{data:y,timestamp:Date.now()}),y}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.users),(0,t.My)(r.FIELD_NAMES.joinedDate,"desc"),(0,t.AB)(e));s&&(a=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.users),(0,t.My)(r.FIELD_NAMES.joinedDate,"desc"),(0,t.HM)(s),(0,t.AB)(e)));let i=await (0,t.getDocs)(a);return{users:i.docs.map(e=>{var s,a;return{id:e.id,...e.data(),joinedDate:null==(s=e.data()[r.FIELD_NAMES.joinedDate])?void 0:s.toDate(),planExpiry:null==(a=e.data()[r.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function o(e){try{if(!e||0===e.trim().length)return[];let s=e.toLowerCase().trim(),a=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.users),(0,t.My)(r.FIELD_NAMES.joinedDate,"desc"));return(await (0,t.getDocs)(a)).docs.map(e=>{var s,a;return{id:e.id,...e.data(),joinedDate:null==(s=e.data()[r.FIELD_NAMES.joinedDate])?void 0:s.toDate(),planExpiry:null==(a=e.data()[r.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}).filter(e=>{let a=String(e[r.FIELD_NAMES.name]||"").toLowerCase(),t=String(e[r.FIELD_NAMES.email]||"").toLowerCase(),l=String(e[r.FIELD_NAMES.mobile]||"").toLowerCase(),i=String(e[r.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(s)||t.includes(s)||l.includes(s)||i.includes(s)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.users),(0,t.My)(r.FIELD_NAMES.joinedDate,"desc"));return(await (0,t.getDocs)(e)).docs.map(e=>{var s,a;return{id:e.id,...e.data(),joinedDate:null==(s=e.data()[r.FIELD_NAMES.joinedDate])?void 0:s.toDate(),planExpiry:null==(a=e.data()[r.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function x(){try{let e=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.users));return(await (0,t.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function h(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.transactions),(0,t.My)(r.FIELD_NAMES.date,"desc"),(0,t.AB)(e));s&&(a=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.transactions),(0,t.My)(r.FIELD_NAMES.date,"desc"),(0,t.HM)(s),(0,t.AB)(e)));let i=await (0,t.getDocs)(a);return{transactions:i.docs.map(e=>{var s;return{id:e.id,...e.data(),date:null==(s=e.data()[r.FIELD_NAMES.date])?void 0:s.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function m(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.withdrawals),(0,t.My)("date","desc"),(0,t.AB)(e));s&&(a=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.withdrawals),(0,t.My)("date","desc"),(0,t.HM)(s),(0,t.AB)(e)));let i=await (0,t.getDocs)(a);return{withdrawals:i.docs.map(e=>{var s;return{id:e.id,...e.data(),date:null==(s=e.data().date)?void 0:s.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function g(e,s){try{await (0,t.mZ)((0,t.H9)(l.db,r.COLLECTIONS.users,e),s),i.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function u(e){try{await (0,t.kd)((0,t.H9)(l.db,r.COLLECTIONS.users,e)),i.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function N(e,s,d){try{let n=await (0,t.x7)((0,t.H9)(l.db,r.COLLECTIONS.withdrawals,e));if(!n.exists())throw Error("Withdrawal not found");let{userId:o,amount:c,status:x}=n.data(),h={status:s,updatedAt:t.Dc.now()};if(d&&(h.adminNotes=d),await (0,t.mZ)((0,t.H9)(l.db,r.COLLECTIONS.withdrawals,e),h),"approved"===s&&"approved"!==x){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await e(o,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(c," processed for transfer")})}if("rejected"===s&&"rejected"!==x){let{updateWalletBalance:e,addTransaction:s}=await Promise.resolve().then(a.bind(a,3592));await e(o,c),await s(o,{type:"withdrawal_rejected",amount:c,description:"Withdrawal rejected - ₹".concat(c," credited back to wallet")})}i.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},7220:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>x});var t=a(5155),l=a(2115),r=a(6874),i=a.n(r),d=a(6766),n=a(6681),o=a(6779),c=a(12);function x(){var e,s,a,r,x;let{user:h,loading:m,isAdmin:g}=(0,n.wC)(),[u,N]=(0,l.useState)(null),[f,j]=(0,l.useState)(!0),[y,v]=(0,l.useState)(!1);(0,l.useEffect)(()=>{g&&w()},[g]);let w=async()=>{try{j(!0);let e=await (0,o.Pn)();N(e)}catch(e){console.error("Error loading dashboard stats:",e)}finally{j(!1)}};return m||f?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"spinner"})}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,t.jsxs)("aside",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform ".concat(y?"translate-x-0":"-translate-x-full"," transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"),children:[(0,t.jsxs)("div",{className:"flex items-center justify-center h-16 bg-gray-900",children:[(0,t.jsx)(d.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:32,height:32,className:"mr-2"}),(0,t.jsx)("span",{className:"text-white text-xl font-bold",children:"MyTube Admin"})]}),(0,t.jsx)("nav",{className:"mt-8",children:(0,t.jsxs)("div",{className:"px-4 space-y-2",children:[(0,t.jsxs)(i(),{href:"/admin",className:"flex items-center px-4 py-2 text-white bg-gray-700 rounded-lg",children:[(0,t.jsx)("i",{className:"fas fa-tachometer-alt mr-3"}),"Dashboard"]}),(0,t.jsxs)(i(),{href:"/admin/users",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-users mr-3"}),"Users"]}),(0,t.jsxs)(i(),{href:"/admin/simple-upload",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-file-csv mr-3"}),"Simple Upload"]}),(0,t.jsxs)(i(),{href:"/admin/transactions",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-exchange-alt mr-3"}),"Transactions"]}),(0,t.jsxs)(i(),{href:"/admin/withdrawals",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-money-bill-wave mr-3"}),"Withdrawals"]}),(0,t.jsxs)(i(),{href:"/admin/notifications",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-bell mr-3"}),"Notifications"]}),(0,t.jsxs)(i(),{href:"/admin/leaves",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-calendar-times mr-3"}),"Leave Management"]}),(0,t.jsxs)(i(),{href:"/admin/settings",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-cog mr-3"}),"Settings"]}),(0,t.jsxs)(i(),{href:"/admin/fix-active-days",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-tools mr-3"}),"Fix Active Days"]}),(0,t.jsxs)(i(),{href:"/admin/daily-active-days",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-calendar-plus mr-3"}),"Daily Active Days"]})]})}),(0,t.jsx)("div",{className:"absolute bottom-4 left-4 right-4",children:(0,t.jsxs)("button",{onClick:()=>{(0,c._f)(null==h?void 0:h.uid,"/admin/login")},className:"w-full flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-sign-out-alt mr-3"}),"Logout"]})})]}),(0,t.jsxs)("div",{className:"lg:ml-64",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,t.jsx)("button",{onClick:()=>v(!y),className:"lg:hidden text-gray-500 hover:text-gray-700",children:(0,t.jsx)("i",{className:"fas fa-bars text-xl"})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("span",{className:"text-gray-700",children:"Welcome, Admin"}),(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,t.jsx)("i",{className:"fas fa-user-shield text-gray-600"})})]})]})}),(0,t.jsxs)("main",{className:"p-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-users text-blue-600 text-xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Users"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==u||null==(e=u.totalUsers)?void 0:e.toLocaleString())||"0"})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-video text-green-600 text-xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Videos"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==u||null==(s=u.totalVideos)?void 0:s.toLocaleString())||"0"})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-rupee-sign text-yellow-600 text-xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Earnings"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₹",(null==u||null==(a=u.totalEarnings)?void 0:a.toLocaleString())||"0"]})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-clock text-red-600 text-xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Withdrawals"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==u?void 0:u.pendingWithdrawals)||"0"})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow mb-8",children:[(0,t.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Today's Activity"})}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-3xl font-bold text-blue-600",children:(null==u?void 0:u.todayUsers)||"0"}),(0,t.jsx)("p",{className:"text-gray-600",children:"New Users"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-3xl font-bold text-green-600",children:(null==u||null==(r=u.todayVideos)?void 0:r.toLocaleString())||"0"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Videos Watched"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("p",{className:"text-3xl font-bold text-yellow-600",children:["₹",(null==u||null==(x=u.todayEarnings)?void 0:x.toLocaleString())||"0"]}),(0,t.jsx)("p",{className:"text-gray-600",children:"Earnings Paid"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-3xl font-bold text-red-600",children:(null==u?void 0:u.todayWithdrawals)||"0"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Withdrawals"})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsx)(i(),{href:"/admin/users",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-users text-blue-600 text-2xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Manage Users"}),(0,t.jsx)("p",{className:"text-gray-600",children:"View and manage user accounts"})]})]})}),(0,t.jsx)(i(),{href:"/admin/withdrawals",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-money-bill-wave text-green-600 text-2xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Process Withdrawals"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Review and approve withdrawals"})]})]})}),(0,t.jsx)(i(),{href:"/admin/notifications",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-bell text-yellow-600 text-2xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Send Notifications"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Notify users about updates"})]})]})}),(0,t.jsx)(i(),{href:"/admin/settings",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 bg-purple-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-cog text-purple-600 text-2xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"System Settings"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Configure platform settings"})]})]})}),(0,t.jsx)(i(),{href:"/admin/fix-active-days",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 bg-red-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-tools text-red-600 text-2xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Fix Active Days"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Fix daily counts and active days"})]})]})}),(0,t.jsx)(i(),{href:"/admin/simple-upload",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-file-csv text-green-600 text-2xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Simple Upload"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Update videos, wallet & active days via CSV"})]})]})}),(0,t.jsx)(i(),{href:"/admin/daily-active-days",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 bg-indigo-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-calendar-plus text-indigo-600 text-2xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Daily Active Days"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage daily active days increment"})]})]})})]})]})]}),y&&(0,t.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>v(!1)})]})}},8856:(e,s,a)=>{Promise.resolve().then(a.bind(a,7220))}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,5181,6874,3063,3592,6681,8441,1684,7358],()=>s(8856)),_N_E=e.O()}]);