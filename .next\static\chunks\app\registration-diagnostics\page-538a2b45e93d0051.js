(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[139],{921:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var s=a(5155),r=a(2115),i=a(3004),c=a(5317),n=a(6104),o=a(3592);function l(){let[e,t]=(0,r.useState)([]),[a,l]=(0,r.useState)(!1),[d,u]=(0,r.useState)(""),m=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",s=new Date().toLocaleTimeString(),r="[".concat(s,"] ").concat("success"===a?"✅":"error"===a?"❌":"warning"===a?"⚠️":"ℹ️"," ").concat(e);t(e=>[...e,r]),console.log(r)},h=()=>{t([])},g=async()=>{l(!0),h();try{var e,t,a;let s=d||"diagnostic".concat(Date.now(),"@test.com");m("\uD83D\uDE80 Starting Registration Diagnostics","info"),m("\uD83D\uDCE7 Test Email: ".concat(s)),m("\uD83D\uDD27 Firebase Project: ".concat("mytube-india")),m("\n=== STEP 1: Firebase Configuration Check ==="),m("API Key: ".concat("Present")),m("Auth Domain: ".concat("mytube-india.firebaseapp.com")),m("Project ID: ".concat("mytube-india")),m("App ID: ".concat("Present")),m("\n=== STEP 2: Firebase Auth Test ===");let r=null;try{m("Creating Firebase Auth user..."),r=(await (0,i.eJ)(n.j2,s,"test123456")).user,m("Auth user created successfully: ".concat(r.uid),"success"),m("User email: ".concat(r.email)),m("Email verified: ".concat(r.emailVerified))}catch(e){throw m("Auth creation failed: ".concat(e.message),"error"),m("Auth error code: ".concat(e.code),"error"),e}m("\n=== STEP 3: Auth State Propagation ==="),m("Waiting 2 seconds for auth state to propagate..."),await new Promise(e=>setTimeout(e,2e3)),m("Current auth user: ".concat(null==(e=n.j2.currentUser)?void 0:e.uid)),m("Auth state matches: ".concat((null==(t=n.j2.currentUser)?void 0:t.uid)===r.uid),(null==(a=n.j2.currentUser)?void 0:a.uid)===r.uid?"success":"warning"),m("\n=== STEP 4: Referral Code Generation ===");let l="";try{m("Generating referral code..."),l=await (0,o.x4)(),m("Referral code generated: ".concat(l),"success")}catch(e){m("Referral code generation failed: ".concat(e.message),"error"),m("Using fallback referral code...","warning"),l="MYN".concat(Date.now().toString().slice(-4)),m("Fallback referral code: ".concat(l))}m("\n=== STEP 5: User Data Preparation ===");let u={[o.FIELD_NAMES.name]:"Diagnostic Test User",[o.FIELD_NAMES.email]:s.toLowerCase(),[o.FIELD_NAMES.mobile]:"9876543210",[o.FIELD_NAMES.referralCode]:l,[o.FIELD_NAMES.referredBy]:"",[o.FIELD_NAMES.referralBonusCredited]:!1,[o.FIELD_NAMES.plan]:"Trial",[o.FIELD_NAMES.planExpiry]:null,[o.FIELD_NAMES.activeDays]:1,[o.FIELD_NAMES.joinedDate]:c.Dc.now(),[o.FIELD_NAMES.wallet]:0,[o.FIELD_NAMES.totalVideos]:0,[o.FIELD_NAMES.todayVideos]:0,[o.FIELD_NAMES.lastVideoDate]:null,[o.FIELD_NAMES.videoDuration]:30,status:"active"};m("User data prepared with ".concat(Object.keys(u).length," fields"),"success"),m("Data fields: ".concat(Object.keys(u).join(", "))),m("\n=== STEP 6: Firestore Document Creation ===");try{let e=(0,c.H9)(n.db,o.COLLECTIONS.users,r.uid);m("Document path: ".concat(e.path)),m("Collection: ".concat(o.COLLECTIONS.users)),m("Document ID: ".concat(r.uid)),m("Attempting to create Firestore document..."),await (0,c.BN)(e,u),m("Firestore document created successfully!","success")}catch(e){throw m("Firestore creation failed: ".concat(e.message),"error"),m("Firestore error code: ".concat(e.code),"error"),m("Full error: ".concat(JSON.stringify(e,null,2)),"error"),e}m("\n=== STEP 7: Document Verification ===");try{let e=(0,c.H9)(n.db,o.COLLECTIONS.users,r.uid),t=await (0,c.x7)(e);if(t.exists()){let e=t.data();m("Document verification successful!","success"),m("Retrieved ".concat(Object.keys(e).length," fields")),m("Name: ".concat(e[o.FIELD_NAMES.name])),m("Email: ".concat(e[o.FIELD_NAMES.email])),m("Plan: ".concat(e[o.FIELD_NAMES.plan]))}else throw m("Document does not exist after creation!","error"),Error("Document verification failed")}catch(e){throw m("Document verification failed: ".concat(e.message),"error"),e}m("\n\uD83C\uDF89 All diagnostics passed! Registration should work.","success")}catch(e){m("\n\uD83D\uDCA5 Diagnostics failed at: ".concat(e.message),"error"),m("Error code: ".concat(e.code||"N/A"),"error"),m("Error stack: ".concat(e.stack),"error")}finally{l(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,s.jsxs)("div",{className:"glass-card p-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-6",children:"Registration Diagnostics"}),(0,s.jsxs)("div",{className:"mb-6 space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Test Email (optional - will auto-generate if empty)"}),(0,s.jsx)("input",{type:"email",value:d,onChange:e=>u(e.target.value),placeholder:"<EMAIL>",className:"form-input"})]}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)("button",{onClick:g,disabled:a,className:"btn-primary",children:a?"Running Diagnostics...":"Run Registration Diagnostics"}),(0,s.jsx)("button",{onClick:h,disabled:a,className:"btn-secondary",children:"Clear Logs"})]})]}),(0,s.jsx)("div",{className:"bg-black/30 rounded-lg p-4 max-h-96 overflow-y-auto",children:(0,s.jsx)("div",{className:"text-white font-mono text-sm space-y-1",children:0===e.length?(0,s.jsx)("div",{className:"text-white/60",children:'Click "Run Registration Diagnostics" to start...'}):e.map((e,t)=>(0,s.jsx)("div",{className:"whitespace-pre-wrap",children:e},t))})}),(0,s.jsxs)("div",{className:"mt-6 p-4 bg-blue-500/20 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-white font-bold mb-2",children:"What this test does:"}),(0,s.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,s.jsx)("li",{children:"• Checks Firebase configuration"}),(0,s.jsx)("li",{children:"• Tests Firebase Auth user creation"}),(0,s.jsx)("li",{children:"• Verifies auth state propagation"}),(0,s.jsx)("li",{children:"• Tests referral code generation"}),(0,s.jsx)("li",{children:"• Attempts Firestore document creation"}),(0,s.jsx)("li",{children:"• Verifies document was created successfully"})]})]}),(0,s.jsxs)("div",{className:"mt-4 space-x-4",children:[(0,s.jsx)("a",{href:"/register",className:"btn-primary inline-block",children:"Go to Registration"}),(0,s.jsx)("a",{href:"/debug-registration-simple",className:"btn-secondary inline-block",children:"Debug Registration"})]})]})})})}},1703:(e,t,a)=>{Promise.resolve().then(a.bind(a,921))},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>l,j2:()=>o});var s=a(3915),r=a(3004),i=a(5317),c=a(858);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),o=(0,r.xI)(n),l=(0,i.aU)(n);(0,c.c7)(n)}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,5181,3592,8441,1684,7358],()=>t(1703)),_N_E=e.O()}]);