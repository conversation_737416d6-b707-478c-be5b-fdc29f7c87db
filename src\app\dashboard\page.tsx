'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useRequireAuth } from '@/hooks/useAuth'
import { useBlockingNotifications } from '@/hooks/useBlockingNotifications'
import { getUserData, getWalletData, getVideoCountData } from '@/lib/dataService'
import { handleUserLogout, clearExpiredSessions } from '@/lib/authUtils'
import NotificationPanel, { NotificationBell } from '@/components/NotificationPanel'
import BlockingNotificationModal from '@/components/BlockingNotificationModal'
import UserLeaveManagement from '@/components/UserLeaveManagement'
import InstallApp from '@/components/InstallApp'

interface UserData {
  name: string
  email: string
  mobile: string
  referralCode: string
  plan: string
  planExpiry: Date | null
  activeDays: number
}

interface WalletData {
  wallet: number
}

interface VideoData {
  totalVideos: number
  todayVideos: number
  remainingVideos: number
}

export default function DashboardPage() {
  const { user, loading } = useRequireAuth()
  const { hasBlockingNotifications, isChecking, markAllAsRead } = useBlockingNotifications(user?.uid || null)
  const [userData, setUserData] = useState<UserData | null>(null)
  const [walletData, setWalletData] = useState<WalletData | null>(null)
  const [videoData, setVideoData] = useState<VideoData | null>(null)
  const [dataLoading, setDataLoading] = useState(true)
  const [showNotifications, setShowNotifications] = useState(false)
  const [usedLeaves, setUsedLeaves] = useState(0)

  useEffect(() => {
    // Clean up expired sessions on dashboard load
    clearExpiredSessions()

    if (user) {
      loadUserData()
    }
  }, [user])

  const loadUserData = async () => {
    try {
      setDataLoading(true)

      // Load user data, wallet data, video data, and leave data in parallel
      const [userResult, walletResult, videoResult] = await Promise.all([
        getUserData(user!.uid),
        getWalletData(user!.uid),
        getVideoCountData(user!.uid),
        loadUserLeaveCount()
      ])

      setUserData(userResult)
      setWalletData(walletResult)
      setVideoData(videoResult)

      // Update active days to ensure live calculation for display
      if (userResult) {
        try {
          const { updateUserActiveDays } = await import('@/lib/dataService')
          await updateUserActiveDays(user!.uid)

          // Reload user data to get updated active days
          const updatedUserData = await getUserData(user!.uid)
          setUserData(updatedUserData)
        } catch (error) {
          console.error('Error updating active days:', error)
        }
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setDataLoading(false)
    }
  }

  const loadUserLeaveCount = async () => {
    try {
      const { getUserMonthlyLeaveCount } = await import('@/lib/leaveService')
      const currentDate = new Date()
      const currentYear = currentDate.getFullYear()
      const currentMonth = currentDate.getMonth() + 1 // getMonth() returns 0-11

      const leaveCount = await getUserMonthlyLeaveCount(user!.uid, currentYear, currentMonth)
      setUsedLeaves(leaveCount)

      console.log(`User ${user!.uid} has used ${leaveCount} leaves this month`)
      return leaveCount
    } catch (error) {
      console.error('Error loading user leave count:', error)
      setUsedLeaves(0) // Default to 0 on error
      return 0
    }
  }

  const handleLogout = () => {
    handleUserLogout(user?.uid, '/login')
  }

  if (loading || dataLoading || isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p className="text-white">
            {loading ? 'Loading...' : isChecking ? 'Checking notifications...' : 'Loading dashboard...'}
          </p>
        </div>
      </div>
    )
  }

  // Show blocking notifications if any exist
  if (hasBlockingNotifications && user) {
    return (
      <BlockingNotificationModal
        userId={user.uid}
        onAllRead={markAllAsRead}
      />
    )
  }

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <header className="glass-card p-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Image
              src="/img/mytube-logo.svg"
              alt="MyTube Logo"
              width={40}
              height={40}
              className="mr-3"
            />
            <div>
              <h1 className="text-xl font-bold text-white">MyTube Dashboard</h1>
              <p className="text-white/80">Welcome back, {userData?.name || 'User'}</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {user && (
              <NotificationBell
                userId={user.uid}
                onClick={() => setShowNotifications(true)}
              />
            )}
            <button
              onClick={handleLogout}
              className="glass-button px-4 py-2 text-white hover:bg-red-500/20 transition-colors"
            >
              <i className="fas fa-sign-out-alt mr-2"></i>
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
        <Link href="/work" className="glass-card p-4 text-center hover:scale-105 transition-transform">
          <i className="fas fa-play-circle text-3xl text-youtube-red mb-2"></i>
          <h3 className="text-white font-semibold">Watch Videos</h3>
        </Link>
        <Link href="/wallet" className="glass-card p-4 text-center hover:scale-105 transition-transform">
          <i className="fas fa-wallet text-3xl text-green-400 mb-2"></i>
          <h3 className="text-white font-semibold">Wallet</h3>
        </Link>
        <Link href="/transactions" className="glass-card p-4 text-center hover:scale-105 transition-transform">
          <i className="fas fa-history text-3xl text-orange-400 mb-2"></i>
          <h3 className="text-white font-semibold">Transactions</h3>
        </Link>
        <Link href="/refer" className="glass-card p-4 text-center hover:scale-105 transition-transform">
          <i className="fas fa-users text-3xl text-blue-400 mb-2"></i>
          <h3 className="text-white font-semibold">Refer & Earn</h3>
        </Link>
        <Link href="/profile" className="glass-card p-4 text-center hover:scale-105 transition-transform">
          <i className="fas fa-user text-3xl text-purple-400 mb-2"></i>
          <h3 className="text-white font-semibold">Profile</h3>
        </Link>
        <Link href="/plans" className="glass-card p-4 text-center hover:scale-105 transition-transform">
          <i className="fas fa-crown text-3xl text-yellow-400 mb-2"></i>
          <h3 className="text-white font-semibold">Plans</h3>
        </Link>
      </div>

      {/* Wallet Overview */}
      <div className="glass-card p-6 mb-6">
        <h2 className="text-xl font-bold text-white mb-4">
          <i className="fas fa-wallet mr-2"></i>
          Wallet Overview
        </h2>
        <div className="bg-green-500/20 p-6 rounded-lg text-center">
          <h3 className="text-green-400 font-semibold mb-2">My Wallet</h3>
          <p className="text-4xl font-bold text-white mb-2">
            ₹{(walletData?.wallet || 0).toFixed(2)}
          </p>
          <p className="text-white/60">Total available balance</p>

          {/* Trial Plan Withdrawal Restriction */}
          {userData?.plan === 'Trial' && (
            <div className="mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <i className="fas fa-lock text-red-400 mr-2"></i>
                <span className="text-red-400 font-medium text-sm">Withdrawal Restricted</span>
              </div>
              <p className="text-white/80 text-xs mb-3">
                Trial users cannot withdraw funds. Upgrade to enable withdrawals.
              </p>
              <Link href="/plans" className="btn-secondary text-xs px-3 py-1">
                <i className="fas fa-arrow-up mr-1"></i>
                Upgrade Plan
              </Link>
            </div>
          )}

          <Link href="/wallet" className="btn-primary mt-4 inline-block">
            <i className="fas fa-eye mr-2"></i>
            View Details
          </Link>
        </div>
      </div>

      {/* Video Progress */}
      <div className="glass-card p-6 mb-6">
        <h2 className="text-xl font-bold text-white mb-4">
          <i className="fas fa-video mr-2"></i>
          Today's Progress
        </h2>
        <div className="grid md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-3xl font-bold text-youtube-red">
              {videoData?.todayVideos || 0}
            </p>
            <p className="text-white/80">Videos Watched</p>
          </div>
          <div className="text-center">
            <p className="text-3xl font-bold text-yellow-400">
              {videoData?.remainingVideos || 0}
            </p>
            <p className="text-white/80">Remaining</p>
          </div>
          <div className="text-center">
            <p className="text-3xl font-bold text-green-400">
              {videoData?.totalVideos || 0}
            </p>
            <p className="text-white/80">Total Videos</p>
          </div>
          <div className="text-center">
            <p className="text-3xl font-bold text-blue-400">
              {userData?.activeDays || 0}
            </p>
            <p className="text-white/80">Active Days</p>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="mt-4">
          <div className="bg-white/20 rounded-full h-3">
            <div
              className="bg-youtube-red h-3 rounded-full transition-all duration-300"
              style={{
                width: `${videoData ? (videoData.todayVideos / 50) * 100 : 0}%`
              }}
            ></div>
          </div>
          <p className="text-white/80 text-sm mt-2 text-center">
            {videoData?.todayVideos || 0} / 50 videos completed today
          </p>
        </div>
      </div>

      {/* User Leave Management */}
      {user && (
        <UserLeaveManagement
          userId={user.uid}
          currentMonth={new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
          usedLeaves={usedLeaves}
          maxLeaves={4}
          onLeaveCountChange={loadUserLeaveCount}
        />
      )}

      {/* Support Section */}
      <div className="glass-card p-6 mb-6">
        <h2 className="text-xl font-bold text-white mb-4">
          <i className="fas fa-headset mr-2"></i>
          Need Help?
        </h2>
        <p className="text-white/60 mb-6">
          Our support team is here to help you with any questions about earning, withdrawals, or your account.
        </p>

        <div className="grid md:grid-cols-2 gap-4">
          <a
            href="https://wa.me/************"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center bg-green-500/20 border border-green-500/30 rounded-lg p-4 hover:bg-green-500/30 transition-colors"
          >
            <i className="fab fa-whatsapp text-green-400 text-2xl mr-4"></i>
            <div>
              <div className="text-white font-semibold">WhatsApp Support</div>
              <div className="text-green-400 text-sm">+91 **********</div>
              <div className="text-white/60 text-xs">9 AM - 6 PM (Working days)</div>
            </div>
          </a>

          <a
            href="mailto:<EMAIL>"
            className="flex items-center bg-blue-500/20 border border-blue-500/30 rounded-lg p-4 hover:bg-blue-500/30 transition-colors"
          >
            <i className="fas fa-envelope text-blue-400 text-2xl mr-4"></i>
            <div>
              <div className="text-white font-semibold">Email Support</div>
              <div className="text-blue-400 text-sm"><EMAIL></div>
              <div className="text-white/60 text-xs">9 AM - 6 PM (Working days)</div>
            </div>
          </a>
        </div>
      </div>

      {/* Install App Section */}
      <InstallApp variant="dashboard" className="mb-6" />

      {/* Notification Panel */}
      {user && (
        <NotificationPanel
          userId={user.uid}
          isOpen={showNotifications}
          onClose={() => setShowNotifications(false)}
        />
      )}
    </div>
  )
}
