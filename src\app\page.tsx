'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import InstallApp from '@/components/InstallApp'
import Image from 'next/image'
import Swal from 'sweetalert2'

export default function HomePage() {
  const [isClearing, setIsClearing] = useState(false)

  useEffect(() => {
    // Silent version check on homepage load (no user disruption)
    const checkVersion = async () => {
      try {
        const { checkVersionAndClearCache } = await import('@/lib/versionService')
        await checkVersionAndClearCache()
      } catch (error) {
        console.error('Silent version check failed:', error)
      }
    }
    checkVersion()
  }, [])

  // Version info for cache management
  const appVersion = "2.1.0"
  const lastUpdated = "2025-01-19"

  const clearCacheAndData = async () => {
    try {
      setIsClearing(true)

      // Show confirmation dialog
      const result = await Swal.fire({
        title: 'Clear Cache & Data?',
        html: `
          <div class="text-left">
            <p class="mb-3"><strong>This will clear:</strong></p>
            <ul class="text-sm space-y-1 mb-4">
              <li>• Browser cache & stored data</li>
              <li>• Local storage & session data</li>
              <li>• Cookies & preferences</li>
              <li>• Cached videos & images</li>
              <li>• All temporary files</li>
            </ul>
            <p class="text-sm text-gray-600">
              <strong>Why clear cache?</strong> Get the latest version of MyTube with all new features and bug fixes.
            </p>
          </div>
        `,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, Clear Everything!',
        cancelButtonText: 'Cancel',
        allowOutsideClick: false
      })

      if (!result.isConfirmed) {
        setIsClearing(false)
        return
      }

      // Show progress
      Swal.fire({
        title: 'Clearing Cache & Data...',
        html: `
          <div class="text-center">
            <div class="spinner mx-auto mb-4"></div>
            <p>Please wait while we clear all cached data...</p>
            <p class="text-sm text-gray-600 mt-2">This may take a few seconds</p>
          </div>
        `,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false
      })

      // Clear localStorage
      try {
        localStorage.clear()
        console.log('✅ localStorage cleared')
      } catch (error) {
        console.warn('⚠️ Could not clear localStorage:', error)
      }

      // Clear sessionStorage
      try {
        sessionStorage.clear()
        console.log('✅ sessionStorage cleared')
      } catch (error) {
        console.warn('⚠️ Could not clear sessionStorage:', error)
      }

      // Clear IndexedDB (if available) with timeout protection
      try {
        if ('indexedDB' in window) {
          // Use known database names instead of indexedDB.databases() which might not be supported
          const knownDatabases = ['firebaseLocalStorageDb', 'firebase-heartbeat-database', 'firebase-installations-database']

          for (const dbName of knownDatabases) {
            try {
              await Promise.race([
                new Promise((resolve, reject) => {
                  const deleteReq = indexedDB.deleteDatabase(dbName)
                  deleteReq.onsuccess = () => resolve(true)
                  deleteReq.onerror = () => resolve(true) // Don't fail if DB doesn't exist
                  deleteReq.onblocked = () => resolve(true) // Continue even if blocked
                }),
                new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 3000))
              ])
            } catch (error) {
              console.warn(`⚠️ Could not clear IndexedDB ${dbName}:`, error)
            }
          }
          console.log('✅ IndexedDB cleared')
        }
      } catch (error) {
        console.warn('⚠️ Could not clear IndexedDB:', error)
      }

      // Clear Service Worker cache (if available) with timeout protection
      try {
        if ('serviceWorker' in navigator && 'caches' in window) {
          const cacheNames = await Promise.race([
            caches.keys(),
            new Promise<string[]>((_, reject) =>
              setTimeout(() => reject(new Error('Cache keys timeout')), 5000)
            )
          ])

          await Promise.race([
            Promise.all(cacheNames.map(cacheName => caches.delete(cacheName))),
            new Promise<void>((_, reject) =>
              setTimeout(() => reject(new Error('Cache deletion timeout')), 5000)
            )
          ])
          console.log('✅ Service Worker cache cleared')
        }
      } catch (error) {
        console.warn('⚠️ Could not clear Service Worker cache:', error)
      }

      // Clear cookies (limited by same-origin policy)
      try {
        document.cookie.split(";").forEach(cookie => {
          const eqPos = cookie.indexOf("=")
          const name = eqPos > -1 ? cookie.substring(0, eqPos) : cookie
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${window.location.hostname}`
        })
        console.log('✅ Cookies cleared')
      } catch (error) {
        console.warn('⚠️ Could not clear cookies:', error)
      }

      // Wait a moment for operations to complete
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Show success and reload
      Swal.fire({
        icon: 'success',
        title: 'Cache & Data Cleared!',
        html: `
          <div class="text-center">
            <p class="mb-3">✅ All cached data has been cleared successfully!</p>
            <p class="text-sm text-gray-600 mb-3">
              The page will now reload to load the latest version of MyTube.
            </p>
            <p class="text-sm text-green-600 font-semibold">
              🎉 You now have the freshest version with all updates!
            </p>
          </div>
        `,
        timer: 3000,
        showConfirmButton: true,
        confirmButtonText: 'Reload Now',
        allowOutsideClick: false
      }).then(() => {
        // Force reload with cache bypass
        window.location.reload()
      })

    } catch (error) {
      console.error('Error clearing cache and data:', error)
      Swal.fire({
        icon: 'error',
        title: 'Clear Failed',
        html: `
          <div class="text-left">
            <p class="mb-2">Some data could not be cleared:</p>
            <p class="text-sm text-gray-600">${error instanceof Error ? error.message : 'Unknown error'}</p>
            <p class="text-sm text-blue-600 mt-3">
              Try refreshing the page manually (Ctrl+F5 or Cmd+Shift+R)
            </p>
          </div>
        `,
        confirmButtonText: 'OK'
      })
    } finally {
      setIsClearing(false)
    }
  }

  return (
    <main className="min-h-screen">
      {/* Minimal Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 p-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <Image
              src="/img/mytube-logo.svg"
              alt="MyTube Logo"
              width={32}
              height={32}
              className="mr-2"
            />
            <span className="text-white text-xl font-bold">MyTube</span>
          </div>
          <div className="flex space-x-2 sm:space-x-4">
            <button
              onClick={clearCacheAndData}
              disabled={isClearing}
              className="nav-link text-sm sm:text-base"
              title="Clear cache & data to get latest version"
            >
              {isClearing ? (
                <>
                  <div className="spinner w-3 h-3 mr-1 sm:mr-2"></div>
                  <span className="hidden sm:inline">Clearing...</span>
                </>
              ) : (
                <>
                  <i className="fas fa-sync-alt mr-1 sm:mr-2"></i>
                  <span className="hidden sm:inline">Clear Cache</span>
                  <span className="sm:hidden">Cache</span>
                </>
              )}
            </button>
            <Link href="#pricing" className="nav-link text-sm sm:text-base">
              <i className="fas fa-crown mr-1 sm:mr-2"></i>
              <span className="hidden sm:inline">Plans</span>
              <span className="sm:hidden">Plans</span>
            </Link>
            <Link href="/login" className="nav-link text-sm sm:text-base">
              <i className="fas fa-sign-in-alt mr-1 sm:mr-2"></i>
              <span className="hidden sm:inline">Login</span>
              <span className="sm:hidden">Login</span>
            </Link>
          </div>
        </div>
      </nav>



      {/* Hero Section */}
      <section className="min-h-screen flex items-center justify-center px-4">
        <div className="max-w-6xl mx-auto text-center">
          <div className="mb-8">
            <div className="flex items-center justify-center mb-6">
              <Image
                src="/img/mytube-logo.svg"
                alt="MyTube Logo"
                width={60}
                height={60}
                className="mr-4"
              />
              <span className="text-4xl font-bold text-white">MyTube</span>
            </div>
            <h1 className="text-5xl md:text-7xl font-bold mb-6 gradient-text">
              Watch Videos & Earn Money
            </h1>
            <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto">
              Watch videos and earn up to ₹30,000 per month. Start your journey to financial freedom today by completing simple video watching tasks!
            </p>
          </div>

          {/* Feature Cards */}
          <div className="grid md:grid-cols-3 gap-6 mb-12">
            <div className="feature-card">
              <i className="fas fa-play-circle text-4xl text-youtube-red mb-4"></i>
              <h3 className="text-xl font-semibold text-white mb-2">Trending Videos</h3>
              <p className="text-white/80">Watch popular content daily</p>
            </div>
            <div className="feature-card">
              <i className="fas fa-money-bill-wave text-4xl text-green-400 mb-4"></i>
              <h3 className="text-xl font-semibold text-white mb-2">Instant Earnings</h3>
              <p className="text-white/80">Get paid for every video watched</p>
            </div>
            <div className="feature-card">
              <i className="fas fa-bolt text-4xl text-yellow-400 mb-4"></i>
              <h3 className="text-xl font-semibold text-white mb-2">Fast & Simple</h3>
              <p className="text-white/80">Easy video watching process</p>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="space-y-4">
            <Link href="/login" className="btn-primary inline-flex items-center text-lg px-8 py-4">
              <i className="fas fa-rocket mr-3"></i>
              Start Earning Now
            </Link>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mt-6">
              <Link href="/how-it-works" className="btn-secondary inline-flex items-center">
                <i className="fas fa-info-circle mr-2"></i>
                How It Works
              </Link>
              <Link href="/faq" className="btn-secondary inline-flex items-center">
                <i className="fas fa-question-circle mr-2"></i>
                FAQ
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Choose Your Earning Plan
            </h2>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Start with our free trial or upgrade to premium plans for higher earnings. Watch videos and earn money with flexible pricing options.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Trial Plan */}
            <div className="glass-card p-8">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-white mb-2">Trial</h3>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-white">Free</span>
                  <span className="text-white/60 ml-2">/ 2 days</span>
                </div>
                <p className="text-green-400 font-semibold">
                  Earn ₹10 per 50 videos
                </p>
              </div>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-3"></i>
                  2 days access
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-3"></i>
                  ₹10 per 50 videos
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-3"></i>
                  Basic support
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-3"></i>
                  Video duration: 30 seconds
                </li>
              </ul>
              <Link href="/register" className="w-full btn-secondary block text-center">
                Start Free Trial
              </Link>
            </div>

            {/* Gold Plan - Most Popular */}
            <div className="glass-card p-8 relative ring-2 ring-yellow-400">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-yellow-400 text-black px-4 py-1 rounded-full text-sm font-bold">
                  Most Popular
                </span>
              </div>
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-white mb-2">Gold</h3>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-white">₹3,999</span>
                  <span className="text-white/60 ml-2">/ 30 days</span>
                </div>
                <p className="text-green-400 font-semibold">
                  Earn ₹200 per 50 videos
                </p>
              </div>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-3"></i>
                  30 days access
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-3"></i>
                  ₹200 per 50 videos
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-3"></i>
                  Video duration: 3 minutes
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-3"></i>
                  Referral bonus: ₹400
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-3"></i>
                  Priority support
                </li>
              </ul>
              <Link href="/plans" className="w-full bg-yellow-400 text-black py-3 rounded-lg font-semibold hover:bg-yellow-500 transition-all duration-300 block text-center">
                Choose Gold
              </Link>
            </div>

            {/* Diamond Plan */}
            <div className="glass-card p-8">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-white mb-2">Diamond</h3>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-white">₹9,999</span>
                  <span className="text-white/60 ml-2">/ 30 days</span>
                </div>
                <p className="text-green-400 font-semibold">
                  Earn ₹400 per 50 videos
                </p>
              </div>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-3"></i>
                  30 days access
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-3"></i>
                  ₹400 per 50 videos
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-3"></i>
                  Video duration: 1 minute
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-3"></i>
                  Referral bonus: ₹1200
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-3"></i>
                  VIP support
                </li>
              </ul>
              <Link href="/plans" className="w-full btn-primary block text-center">
                Choose Diamond
              </Link>
            </div>
          </div>

          <div className="text-center mt-12">
            <Link href="/plans" className="btn-secondary inline-flex items-center">
              <i className="fas fa-crown mr-2"></i>
              View All Plans
            </Link>
          </div>
        </div>
      </section>

      {/* Support Section */}
      <section className="py-20 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Need Help?
          </h2>
          <p className="text-xl text-white/80 mb-12 max-w-2xl mx-auto">
            Our support team is here to help you get started and answer any questions about earning with MyTube.
          </p>

          {/* Clear Cache Section */}
          <div className="glass-card p-8 mb-12 max-w-2xl mx-auto clear-cache-section">
            <div className="text-center">
              <i className="fas fa-sync-alt text-5xl text-blue-400 mb-4 clear-cache-icon"></i>
              <h3 className="text-2xl font-bold text-white mb-4">Get Latest Version</h3>
              <p className="text-white/80 mb-6">
                Having issues or want the latest features? Clear your cache and data to get the freshest version of MyTube with all updates and bug fixes.
              </p>
              <button
                onClick={clearCacheAndData}
                disabled={isClearing}
                className={`clear-cache-btn inline-flex items-center text-lg px-8 py-4 text-white font-semibold rounded-lg ${
                  isClearing ? 'btn-processing' : ''
                }`}
              >
                {isClearing ? (
                  <>
                    <div className="spinner mr-3 w-5 h-5"></div>
                    Clearing Cache...
                  </>
                ) : (
                  <>
                    <i className="fas fa-sync-alt mr-3"></i>
                    Clear Cache & Data
                  </>
                )}
              </button>
              <p className="text-white/60 text-sm mt-4">
                ✨ Recommended if you're experiencing issues or want the latest features
              </p>

              {/* Version Info */}
              <div className="mt-4 inline-flex items-center bg-white/10 rounded-full px-4 py-2 text-xs text-white/80">
                <i className="fas fa-code-branch mr-2"></i>
                Version {appVersion} • Updated {lastUpdated}
              </div>

              <div className="mt-6 grid grid-cols-2 gap-4 text-xs text-white/60">
                <div className="flex items-center justify-center">
                  <i className="fas fa-database mr-2"></i>
                  Clears Storage
                </div>
                <div className="flex items-center justify-center">
                  <i className="fas fa-cookie-bite mr-2"></i>
                  Removes Cookies
                </div>
                <div className="flex items-center justify-center">
                  <i className="fas fa-memory mr-2"></i>
                  Clears Cache
                </div>
                <div className="flex items-center justify-center">
                  <i className="fas fa-download mr-2"></i>
                  Fresh Download
                </div>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <a
              href="https://wa.me/917676636990"
              target="_blank"
              rel="noopener noreferrer"
              className="glass-card p-8 hover:scale-105 transition-transform"
            >
              <i className="fab fa-whatsapp text-5xl text-green-400 mb-4"></i>
              <h3 className="text-xl font-bold text-white mb-2">WhatsApp Support</h3>
              <p className="text-white/80 mb-4">Get instant help via WhatsApp</p>
              <p className="text-green-400 font-semibold">+91 7676636990</p>
              <p className="text-white/60 text-sm mt-2">9 AM - 6 PM (Working days)</p>
            </a>

            <a
              href="mailto:<EMAIL>"
              className="glass-card p-8 hover:scale-105 transition-transform"
            >
              <i className="fas fa-envelope text-5xl text-blue-400 mb-4"></i>
              <h3 className="text-xl font-bold text-white mb-2">Email Support</h3>
              <p className="text-white/80 mb-4">Send us detailed queries</p>
              <p className="text-blue-400 font-semibold"><EMAIL></p>
              <p className="text-white/60 text-sm mt-2">9 AM - 6 PM (Working days)</p>
            </a>

            <InstallApp variant="homepage" />
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 border-t border-white/20">
        <div className="max-w-6xl mx-auto text-center">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <h3 className="text-2xl font-bold text-white mb-2">MyTube</h3>
              <p className="text-white/60">Earn money by watching videos</p>
            </div>
            <div className="text-white/60 text-sm">
              <p>&copy; 2024 MyTube. All rights reserved.</p>
              <div className="mt-2 flex items-center justify-center md:justify-end">
                <i className="fas fa-code-branch mr-2 text-xs"></i>
                <span className="text-xs">v{appVersion}</span>
                <span className="mx-2">•</span>
                <button
                  onClick={clearCacheAndData}
                  disabled={isClearing}
                  className="text-xs text-blue-400 hover:text-blue-300 transition-colors duration-200 underline"
                >
                  {isClearing ? 'Clearing...' : 'Clear Cache'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </main>
  )
}
