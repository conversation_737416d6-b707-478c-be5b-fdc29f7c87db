(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[773,6779],{5067:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var s=a(5155),i=a(2115),l=a(6874),r=a.n(l),n=a(6681),d=a(6779),o=a(4752),c=a.n(o);function m(){let{user:e,loading:t,isAdmin:l}=(0,n.wC)(),[o,m]=(0,i.useState)(!1),[u,h]=(0,i.useState)(null),[x,p]=(0,i.useState)(null),[w,v]=(0,i.useState)([]),[g,y]=(0,i.useState)(!1),f=async()=>{if(x)try{m(!0);let e=(await x.text()).split("\n").filter(e=>e.trim());if(e.length<2)throw Error("CSV file must have at least a header row and one data row");let t=e[0],a=t.includes("	")?"	":",",s=t.split(a).map(e=>e.trim().replace(/"/g,"").toLowerCase());if(["email"].filter(e=>!s.some(t=>t.includes(e))).length>0)throw Error("Missing required column: email");let i=e.slice(1).map((e,t)=>{let i=e.split(a).map(e=>e.trim().replace(/"/g,"")),l={};s.forEach((e,t)=>{l[e]=i[t]||""});let r=l.email||"",n=parseInt(l.totalvideos||l.videos||l.totalVideos||"0")||0,d=parseFloat(l.walletbalance||l.wallet||l.walletBalance||"0")||0,o=parseInt(l.activedays||l.active||l.activeDays||"0")||0,c=parseInt(l.quickvideodays||l.quickdays||l.quickVideoRemainingDays||l.quickVideoDays||"0")||0;if(!r)throw Error("Row ".concat(t+2,": Email is required"));if(!r.includes("@"))throw Error("Row ".concat(t+2,": Invalid email format"));return{email:r,totalVideos:n,walletBalance:d,activeDays:o,quickVideoRemainingDays:c}});v(i.slice(0,10)),y(!0)}catch(e){console.error("Error previewing file:",e),c().fire({icon:"error",title:"Preview Failed",text:e.message||"Failed to preview file. Please check the format."})}finally{m(!1)}},b=async()=>{if(x&&(await c().fire({icon:"question",title:"Confirm Data Upload",html:'\n        <div class="text-left">\n          <p><strong>Are you sure you want to update user data from this file?</strong></p>\n          <br>\n          <p>This will:</p>\n          <ul>\n            <li>Find users by email address</li>\n            <li>Add to their existing total videos count (if provided)</li>\n            <li>Add to their existing wallet balance (if provided)</li>\n            <li>SET their active days to the specified value (if provided)</li>\n            <li>SET their quick video remaining days (if provided)</li>\n            <li>Skip users not found in the system</li>\n          </ul>\n          <br>\n          <p class="text-yellow-600"><strong>Note:</strong> Videos and wallet will be ADDED, but active days and quick video days will be SET (replaced)!</p>\n        </div>\n      ',showCancelButton:!0,confirmButtonText:"Yes, Update Users",cancelButtonText:"Cancel",confirmButtonColor:"#dc2626"})).isConfirmed)try{m(!0),h(null),c().fire({title:"Updating Users",html:'\n          <div class="text-center">\n            <div class="spinner mx-auto mb-4"></div>\n            <p>Processing user updates...</p>\n            <p class="text-sm text-gray-600 mt-2">Please wait...</p>\n          </div>\n        ',allowOutsideClick:!1,allowEscapeKey:!1,showConfirmButton:!1});let e=(await x.text()).split("\n").filter(e=>e.trim()),t=e[0],s=t.includes("	")?"	":",",i=t.split(s).map(e=>e.trim().replace(/"/g,"").toLowerCase()),l=e.slice(1).map(e=>{let t=e.split(s).map(e=>e.trim().replace(/"/g,"")),a={};return i.forEach((e,s)=>{a[e]=t[s]||""}),{email:a.email||"",totalVideos:parseInt(a.totalvideos||a.videos||a.totalVideos||"0")||0,walletBalance:parseFloat(a.walletbalance||a.wallet||a.walletBalance||"0")||0,activeDays:parseInt(a.activedays||a.active||a.activeDays||"0")||0,quickVideoRemainingDays:parseInt(a.quickvideodays||a.quickdays||a.quickVideoRemainingDays||a.quickVideoDays||"0")||0}}).filter(e=>e.email),r=0,n=0,o=0,u=[];for(let e of l)try{let t=(await (0,d.x5)(e.email)).find(t=>{var a;return(null==(a=t.email)?void 0:a.toLowerCase())===e.email.toLowerCase()});if(!t){o++,u.push("User not found: ".concat(e.email));continue}let{getWalletData:s,getVideoCountData:i}=await Promise.resolve().then(a.bind(a,3592)),[l,n]=await Promise.all([s(t.id),i(t.id)]),c={};if(e.totalVideos>0&&(c.totalVideos=(n.totalVideos||0)+e.totalVideos),e.walletBalance>0&&(c.wallet=(l.wallet||0)+e.walletBalance),e.activeDays>0&&(c.activeDays=e.activeDays,c.manuallySetActiveDays=!0),void 0!==e.quickVideoRemainingDays&&e.quickVideoRemainingDays>=0)if(c.quickVideoAdvantageRemainingDays=e.quickVideoRemainingDays,e.quickVideoRemainingDays>0){c.quickVideoAdvantage=!0;let t=new Date;t.setDate(t.getDate()+e.quickVideoRemainingDays),c.quickVideoAdvantageExpiry=t}else c.quickVideoAdvantage=!1,c.quickVideoAdvantageExpiry=null;Object.keys(c).length>0&&await (0,d.TK)(t.id,c),r++}catch(t){n++,u.push("Failed to update ".concat(e.email,": ").concat(t.message))}c().close();let p={success:r,failed:n,errors:u,notFound:o};h(p),c().fire({icon:r>0?n>0||o>0?"warning":"success":"error",title:"Update Complete",html:'\n          <div class="text-left">\n            <p><strong>Update Summary:</strong></p>\n            <ul>\n              <li class="text-green-600">✓ Successfully updated: '.concat(r,' users</li>\n              <li class="text-yellow-600">⚠ Not found: ').concat(o,' users</li>\n              <li class="text-red-600">✗ Failed: ').concat(n," users</li>\n            </ul>\n            ").concat(u.length>0?"<br><p><strong>First 5 errors:</strong></p><ul>".concat(u.slice(0,5).map(e=>'<li class="text-red-600 text-sm">'.concat(e,"</li>")).join(""),"</ul>"):"","\n          </div>\n        "),timer:n>0?void 0:5e3,showConfirmButton:n>0})}catch(e){console.error("Error updating users:",e),c().fire({icon:"error",title:"Update Failed",text:e.message||"Failed to update users. Please try again."})}finally{m(!1)}};return t?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Simple User Update"}),(0,s.jsx)("p",{className:"text-white/80",children:"Update user videos, wallet balance, active days, and quick video advantage via CSV"})]}),(0,s.jsxs)(r(),{href:"/admin/users",className:"btn-secondary",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Users"]})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload CSV File"]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Sample File"}),(0,s.jsxs)("button",{onClick:()=>{let e=new Blob(["email,totalVideos,walletBalance,activeDays,quickVideoDays\<EMAIL>,100,500,15,7\<EMAIL>,250,1200,25,0\<EMAIL>,75,300,5,3"],{type:"text/csv"}),t=URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download="simple-upload-sample.csv",a.click(),URL.revokeObjectURL(t)},className:"btn-secondary text-sm",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Download Sample CSV"]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Select CSV File"}),(0,s.jsx)("input",{type:"file",accept:".csv,.txt",onChange:e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];a&&(p(a),v([]),y(!1),h(null))},className:"form-input"})]}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)("button",{onClick:f,disabled:!x||o,className:"btn-secondary",children:o?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Processing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-eye mr-2"}),"Preview Data"]})}),(0,s.jsx)("button",{onClick:b,disabled:!x||o||!g,className:"btn-primary",children:o?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Updating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-upload mr-2"}),"Update Users"]})})]})})]}),g&&w.length>0&&(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-table mr-2"}),"Data Preview (First 10 Records)"]}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-white",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-white/20",children:[(0,s.jsx)("th",{className:"text-left p-2",children:"Email"}),(0,s.jsx)("th",{className:"text-left p-2",children:"Add Videos"}),(0,s.jsx)("th",{className:"text-left p-2",children:"Add Wallet (₹)"}),(0,s.jsx)("th",{className:"text-left p-2",children:"Set Active Days"}),(0,s.jsx)("th",{className:"text-left p-2",children:"Set Quick Video Days"})]})}),(0,s.jsx)("tbody",{children:w.map((e,t)=>(0,s.jsxs)("tr",{className:"border-b border-white/10",children:[(0,s.jsx)("td",{className:"p-2",children:e.email}),(0,s.jsx)("td",{className:"p-2",children:e.totalVideos>0?"+".concat(e.totalVideos):"-"}),(0,s.jsx)("td",{className:"p-2",children:e.walletBalance>0?"+₹".concat(e.walletBalance):"-"}),(0,s.jsx)("td",{className:"p-2",children:e.activeDays>0?e.activeDays:"-"}),(0,s.jsx)("td",{className:"p-2",children:void 0!==e.quickVideoRemainingDays&&e.quickVideoRemainingDays>=0?e.quickVideoRemainingDays:"-"})]},t))})]})})]}),u&&(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-chart-bar mr-2"}),"Update Results"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,s.jsxs)("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-green-400 text-2xl font-bold",children:u.success}),(0,s.jsx)("div",{className:"text-green-300 text-sm",children:"Successfully Updated"})]}),(0,s.jsxs)("div",{className:"bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-yellow-400 text-2xl font-bold",children:u.notFound}),(0,s.jsx)("div",{className:"text-yellow-300 text-sm",children:"Users Not Found"})]}),(0,s.jsxs)("div",{className:"bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-red-400 text-2xl font-bold",children:u.failed}),(0,s.jsx)("div",{className:"text-red-300 text-sm",children:"Failed Updates"})]})]}),u.errors.length>0&&(0,s.jsxs)("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"text-red-400 font-bold mb-2",children:"Errors:"}),(0,s.jsxs)("ul",{className:"text-red-300 text-sm space-y-1",children:[u.errors.slice(0,10).map((e,t)=>(0,s.jsxs)("li",{children:["• ",e]},t)),u.errors.length>10&&(0,s.jsxs)("li",{className:"text-red-400",children:["... and ",u.errors.length-10," more errors"]})]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Instructions"]}),(0,s.jsxs)("div",{className:"text-white/80 space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-bold text-white mb-2",children:"CSV Format:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"email:"})," User's email address (required - must exist in system)"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"totalVideos:"})," Number of videos to ADD to current count (optional)"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"walletBalance:"})," Amount to ADD to current wallet balance (optional)"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"activeDays:"})," Active days to SET (replace current value) (optional)"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"quickVideoDays:"})," Quick video remaining days to SET (0 to disable, >0 to enable) (optional)"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-bold text-white mb-2",children:"Important Notes:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,s.jsx)("li",{children:"Videos and wallet values are ADDED to existing data"}),(0,s.jsx)("li",{children:"Active days and quick video days values REPLACE the current values"}),(0,s.jsx)("li",{children:"Only email is required - other fields are optional"}),(0,s.jsx)("li",{children:"Users must already exist in the system"}),(0,s.jsx)("li",{children:"Email addresses are case-insensitive"}),(0,s.jsx)("li",{children:"Use comma or tab as delimiter"}),(0,s.jsx)("li",{children:"Quick video advantage is automatically enabled when quickVideoDays > 0"}),(0,s.jsx)("li",{children:"Setting quickVideoDays to 0 will disable quick video advantage"})]})]})]})]})]})})}},6779:(e,t,a)=>{"use strict";a.d(t,{CF:()=>c,I0:()=>u,Pn:()=>n,TK:()=>x,getWithdrawals:()=>h,hG:()=>p,lo:()=>d,nQ:()=>m,updateWithdrawalStatus:()=>w,x5:()=>o});var s=a(5317),i=a(6104),l=a(3592);let r=new Map;async function n(){let e="dashboard-stats",t=function(e){let t=r.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=s.Dc.fromDate(t),n=await (0,s.getDocs)((0,s.collection)(i.db,l.COLLECTIONS.users)),d=n.size,o=(0,s.P)((0,s.collection)(i.db,l.COLLECTIONS.users),(0,s._M)(l.FIELD_NAMES.joinedDate,">=",a)),c=(await (0,s.getDocs)(o)).size,m=0,u=0,h=0,x=0;n.forEach(e=>{var a;let s=e.data();m+=s[l.FIELD_NAMES.totalVideos]||0,u+=s[l.FIELD_NAMES.wallet]||0;let i=null==(a=s[l.FIELD_NAMES.lastVideoDate])?void 0:a.toDate();i&&i.toDateString()===t.toDateString()&&(h+=s[l.FIELD_NAMES.todayVideos]||0)});try{let e=(0,s.P)((0,s.collection)(i.db,l.COLLECTIONS.transactions),(0,s._M)(l.FIELD_NAMES.type,"==","video_earning"),(0,s.AB)(1e3));(await (0,s.getDocs)(e)).forEach(e=>{var a;let s=e.data(),i=null==(a=s[l.FIELD_NAMES.date])?void 0:a.toDate();i&&i>=t&&(x+=s[l.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let p=(0,s.P)((0,s.collection)(i.db,l.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending")),w=(await (0,s.getDocs)(p)).size,v=(0,s.P)((0,s.collection)(i.db,l.COLLECTIONS.withdrawals),(0,s._M)("date",">=",a)),g=(await (0,s.getDocs)(v)).size,y={totalUsers:d,totalVideos:m,totalEarnings:u,pendingWithdrawals:w,todayUsers:c,todayVideos:h,todayEarnings:x,todayWithdrawals:g};return r.set(e,{data:y,timestamp:Date.now()}),y}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function d(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,s.P)((0,s.collection)(i.db,l.COLLECTIONS.users),(0,s.My)(l.FIELD_NAMES.joinedDate,"desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.collection)(i.db,l.COLLECTIONS.users),(0,s.My)(l.FIELD_NAMES.joinedDate,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let r=await (0,s.getDocs)(a);return{users:r.docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[l.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[l.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:r.docs[r.docs.length-1]||null,hasMore:r.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function o(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),a=(0,s.P)((0,s.collection)(i.db,l.COLLECTIONS.users),(0,s.My)(l.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(a)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[l.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[l.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}).filter(e=>{let a=String(e[l.FIELD_NAMES.name]||"").toLowerCase(),s=String(e[l.FIELD_NAMES.email]||"").toLowerCase(),i=String(e[l.FIELD_NAMES.mobile]||"").toLowerCase(),r=String(e[l.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(t)||s.includes(t)||i.includes(t)||r.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,s.P)((0,s.collection)(i.db,l.COLLECTIONS.users),(0,s.My)(l.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(e)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[l.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[l.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function m(){try{let e=(0,s.P)((0,s.collection)(i.db,l.COLLECTIONS.users));return(await (0,s.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function u(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,s.P)((0,s.collection)(i.db,l.COLLECTIONS.transactions),(0,s.My)(l.FIELD_NAMES.date,"desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.collection)(i.db,l.COLLECTIONS.transactions),(0,s.My)(l.FIELD_NAMES.date,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let r=await (0,s.getDocs)(a);return{transactions:r.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[l.FIELD_NAMES.date])?void 0:t.toDate()}}),lastDoc:r.docs[r.docs.length-1]||null,hasMore:r.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function h(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,s.P)((0,s.collection)(i.db,l.COLLECTIONS.withdrawals),(0,s.My)("date","desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.collection)(i.db,l.COLLECTIONS.withdrawals),(0,s.My)("date","desc"),(0,s.HM)(t),(0,s.AB)(e)));let r=await (0,s.getDocs)(a);return{withdrawals:r.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}}),lastDoc:r.docs[r.docs.length-1]||null,hasMore:r.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function x(e,t){try{await (0,s.mZ)((0,s.H9)(i.db,l.COLLECTIONS.users,e),t),r.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function p(e){try{await (0,s.kd)((0,s.H9)(i.db,l.COLLECTIONS.users,e)),r.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function w(e,t,n){try{let d=await (0,s.x7)((0,s.H9)(i.db,l.COLLECTIONS.withdrawals,e));if(!d.exists())throw Error("Withdrawal not found");let{userId:o,amount:c,status:m}=d.data(),u={status:t,updatedAt:s.Dc.now()};if(n&&(u.adminNotes=n),await (0,s.mZ)((0,s.H9)(i.db,l.COLLECTIONS.withdrawals,e),u),"approved"===t&&"approved"!==m){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await e(o,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(c," processed for transfer")})}if("rejected"===t&&"rejected"!==m){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await e(o,c),await t(o,{type:"withdrawal_rejected",amount:c,description:"Withdrawal rejected - ₹".concat(c," credited back to wallet")})}r.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},8576:(e,t,a)=>{Promise.resolve().then(a.bind(a,5067))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,6681,8441,1684,7358],()=>t(8576)),_N_E=e.O()}]);