// Version management service to handle automatic cache clearing on deployments
import { doc, getDoc, setDoc } from 'firebase/firestore'
import { db } from './firebase'

// Current app version - increment this with each deployment
const CURRENT_VERSION = '1.0.5'

// Version tracking in Firestore
const VERSION_DOC_PATH = 'system/version'

// Local storage keys
const LOCAL_VERSION_KEY = 'mytube_app_version'
const LAST_CACHE_CLEAR_KEY = 'mytube_last_cache_clear'

// Check if app version has changed and clear cache if needed
export async function checkVersionAndClearCache(): Promise<boolean> {
  try {
    console.log('🔍 Checking app version for cache management...')
    
    // Get stored local version
    const localVersion = localStorage.getItem(LOCAL_VERSION_KEY)
    
    // Get server version from Firestore
    let serverVersion = CURRENT_VERSION
    try {
      const versionDoc = await getDoc(doc(db, VERSION_DOC_PATH))
      if (versionDoc.exists()) {
        serverVersion = versionDoc.data()?.version || CURRENT_VERSION
      }
    } catch (error) {
      console.warn('Could not fetch server version, using current version:', error)
    }
    
    // Check if version has changed
    const versionChanged = !localVersion || localVersion !== serverVersion
    
    if (versionChanged) {
      console.log(`🔄 Version changed: ${localVersion || 'none'} → ${serverVersion}`)
      
      // Clear all cache and local storage data
      await clearApplicationCache()
      
      // Update local version
      localStorage.setItem(LOCAL_VERSION_KEY, serverVersion)
      localStorage.setItem(LAST_CACHE_CLEAR_KEY, new Date().toISOString())
      
      console.log('✅ Cache cleared due to version update')
      return true
    }
    
    console.log('✅ Version unchanged, no cache clearing needed')
    return false
  } catch (error) {
    console.error('Error checking version:', error)
    return false
  }
}

// Clear all application cache and data
export async function clearApplicationCache(): Promise<void> {
  try {
    console.log('🧹 Clearing application cache and data...')
    
    // Clear localStorage (except authentication data)
    const authKeys = ['firebase:authUser', 'firebase:host']
    const preservedData: { [key: string]: string | null } = {}
    
    // Preserve auth data
    authKeys.forEach(key => {
      const keys = Object.keys(localStorage).filter(k => k.includes(key))
      keys.forEach(k => {
        preservedData[k] = localStorage.getItem(k)
      })
    })
    
    // Clear all localStorage
    localStorage.clear()
    
    // Restore auth data
    Object.entries(preservedData).forEach(([key, value]) => {
      if (value) {
        localStorage.setItem(key, value)
      }
    })
    
    // Clear sessionStorage
    sessionStorage.clear()
    
    // Clear browser cache if supported
    if ('caches' in window) {
      const cacheNames = await caches.keys()
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      )
      console.log('🗑️ Browser caches cleared')
    }
    
    // Clear IndexedDB if needed
    if ('indexedDB' in window) {
      try {
        // Clear any IndexedDB databases used by the app
        const databases = ['firebaseLocalStorageDb']
        for (const dbName of databases) {
          const deleteReq = indexedDB.deleteDatabase(dbName)
          await new Promise((resolve, reject) => {
            deleteReq.onsuccess = () => resolve(true)
            deleteReq.onerror = () => reject(deleteReq.error)
            deleteReq.onblocked = () => resolve(true) // Continue even if blocked
          })
        }
        console.log('🗑️ IndexedDB cleared')
      } catch (error) {
        console.warn('Could not clear IndexedDB:', error)
      }
    }
    
    console.log('✅ Application cache cleared successfully')
  } catch (error) {
    console.error('Error clearing cache:', error)
    throw error
  }
}

// Update server version (admin function)
export async function updateServerVersion(newVersion: string): Promise<void> {
  try {
    await setDoc(doc(db, VERSION_DOC_PATH), {
      version: newVersion,
      updatedAt: new Date().toISOString(),
      updatedBy: 'admin'
    })
    
    console.log(`✅ Server version updated to ${newVersion}`)
  } catch (error) {
    console.error('Error updating server version:', error)
    throw error
  }
}

// Get current version info
export function getVersionInfo() {
  return {
    currentVersion: CURRENT_VERSION,
    localVersion: localStorage.getItem(LOCAL_VERSION_KEY),
    lastCacheClear: localStorage.getItem(LAST_CACHE_CLEAR_KEY)
  }
}

// Manual cache clear function for users
export async function manualCacheClear(): Promise<void> {
  try {
    await clearApplicationCache()
    localStorage.setItem(LAST_CACHE_CLEAR_KEY, new Date().toISOString())
    
    // Show success message and reload
    alert('Cache cleared successfully! The page will now reload to apply changes.')
    window.location.reload()
  } catch (error) {
    console.error('Error during manual cache clear:', error)
    alert('Error clearing cache. Please try refreshing the page manually.')
  }
}
