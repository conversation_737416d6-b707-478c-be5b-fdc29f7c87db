// Version management service to handle automatic cache clearing on deployments
import { doc, getDoc, setDoc } from 'firebase/firestore'
import { db } from './firebase'

// Current app version - increment this with each deployment
const CURRENT_VERSION = '1.0.5'

// Version tracking in Firestore
const VERSION_DOC_PATH = 'system/version'

// Local storage keys
const LOCAL_VERSION_KEY = 'mytube_app_version'
const LAST_CACHE_CLEAR_KEY = 'mytube_last_cache_clear'

// Check if app version has changed and clear cache if needed
export async function checkVersionAndClearCache(): Promise<boolean> {
  try {
    console.log('🔍 Checking app version for cache management...')
    
    // Get stored local version
    const localVersion = localStorage.getItem(LOCAL_VERSION_KEY)
    
    // Get server version from Firestore
    let serverVersion = CURRENT_VERSION
    try {
      const versionDoc = await getDoc(doc(db, VERSION_DOC_PATH))
      if (versionDoc.exists()) {
        serverVersion = versionDoc.data()?.version || CURRENT_VERSION
      }
    } catch (error) {
      console.warn('Could not fetch server version, using current version:', error)
    }
    
    // Check if version has changed
    const versionChanged = !localVersion || localVersion !== serverVersion
    
    if (versionChanged) {
      console.log(`🔄 Version changed: ${localVersion || 'none'} → ${serverVersion}`)
      
      // Clear all cache and local storage data
      await clearApplicationCache()
      
      // Update local version
      localStorage.setItem(LOCAL_VERSION_KEY, serverVersion)
      localStorage.setItem(LAST_CACHE_CLEAR_KEY, new Date().toISOString())
      
      console.log('✅ Cache cleared due to version update')
      return true
    }
    
    console.log('✅ Version unchanged, no cache clearing needed')
    return false
  } catch (error) {
    console.error('Error checking version:', error)
    return false
  }
}

// Clear all application cache and data
export async function clearApplicationCache(): Promise<void> {
  try {
    console.log('🧹 Clearing application cache and data...')

    // Clear localStorage (except authentication data)
    const authKeys = ['firebase:authUser', 'firebase:host']
    const preservedData: { [key: string]: string | null } = {}

    // Preserve auth data
    authKeys.forEach(key => {
      const keys = Object.keys(localStorage).filter(k => k.includes(key))
      keys.forEach(k => {
        preservedData[k] = localStorage.getItem(k)
      })
    })

    // Clear all localStorage
    localStorage.clear()

    // Restore auth data
    Object.entries(preservedData).forEach(([key, value]) => {
      if (value) {
        localStorage.setItem(key, value)
      }
    })

    // Clear sessionStorage
    sessionStorage.clear()
    console.log('🗑️ Storage cleared')

    // Clear browser cache if supported
    if ('caches' in window) {
      try {
        const cacheNames = await Promise.race([
          caches.keys(),
          new Promise<string[]>((_, reject) =>
            setTimeout(() => reject(new Error('Cache keys timeout')), 5000)
          )
        ])

        await Promise.race([
          Promise.all(cacheNames.map(cacheName => caches.delete(cacheName))),
          new Promise<void>((_, reject) =>
            setTimeout(() => reject(new Error('Cache deletion timeout')), 5000)
          )
        ])
        console.log('🗑️ Browser caches cleared')
      } catch (error) {
        console.warn('Could not clear browser caches:', error)
      }
    }

    // Clear IndexedDB with timeout protection
    if ('indexedDB' in window) {
      try {
        // Use a more conservative approach for IndexedDB
        const dbNames = ['firebaseLocalStorageDb', 'firebase-heartbeat-database', 'firebase-installations-database']

        for (const dbName of dbNames) {
          try {
            await Promise.race([
              new Promise<void>((resolve, reject) => {
                const deleteReq = indexedDB.deleteDatabase(dbName)
                deleteReq.onsuccess = () => resolve()
                deleteReq.onerror = () => resolve() // Don't fail if DB doesn't exist
                deleteReq.onblocked = () => resolve() // Continue even if blocked

                // Timeout after 3 seconds
                setTimeout(() => resolve(), 3000)
              }),
              new Promise<void>((_, reject) =>
                setTimeout(() => reject(new Error('IndexedDB timeout')), 5000)
              )
            ])
          } catch (error) {
            console.warn(`Could not clear IndexedDB ${dbName}:`, error)
          }
        }
        console.log('🗑️ IndexedDB cleared')
      } catch (error) {
        console.warn('Could not clear IndexedDB:', error)
      }
    }

    console.log('✅ Application cache cleared successfully')
  } catch (error) {
    console.error('Error clearing cache:', error)
    throw error
  }
}

// Update server version (admin function)
export async function updateServerVersion(newVersion: string): Promise<void> {
  try {
    await setDoc(doc(db, VERSION_DOC_PATH), {
      version: newVersion,
      updatedAt: new Date().toISOString(),
      updatedBy: 'admin'
    })
    
    console.log(`✅ Server version updated to ${newVersion}`)
  } catch (error) {
    console.error('Error updating server version:', error)
    throw error
  }
}

// Simple cache clear (fallback function)
export function simpleCacheClear(): void {
  try {
    // Clear storage
    localStorage.clear()
    sessionStorage.clear()

    // Clear cookies
    document.cookie.split(";").forEach(cookie => {
      const eqPos = cookie.indexOf("=")
      const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim()
      if (name) {
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
      }
    })

    console.log('✅ Simple cache clear completed')
  } catch (error) {
    console.error('Error in simple cache clear:', error)
  }
}

// Get current version info
export function getVersionInfo() {
  return {
    currentVersion: CURRENT_VERSION,
    localVersion: localStorage.getItem(LOCAL_VERSION_KEY),
    lastCacheClear: localStorage.getItem(LAST_CACHE_CLEAR_KEY)
  }
}

// Manual cache clear function for users
export async function manualCacheClear(): Promise<void> {
  try {
    // Add timeout to the entire operation
    await Promise.race([
      clearApplicationCache(),
      new Promise<void>((_, reject) =>
        setTimeout(() => reject(new Error('Cache clear operation timed out')), 15000)
      )
    ])

    localStorage.setItem(LAST_CACHE_CLEAR_KEY, new Date().toISOString())

    // Show success message and reload
    alert('Cache cleared successfully! The page will now reload to apply changes.')
    window.location.reload()
  } catch (error) {
    console.error('Error during manual cache clear:', error)

    // Try a simple reload as fallback
    const fallback = confirm(
      'Cache clearing encountered an issue. Would you like to try a simple page refresh instead?'
    )

    if (fallback) {
      window.location.reload()
    } else {
      alert('Please try refreshing the page manually (Ctrl+F5 or Cmd+Shift+R)')
    }
  }
}
