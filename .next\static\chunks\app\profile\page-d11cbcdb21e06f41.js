(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{1510:(e,s,a)=>{Promise.resolve().then(a.bind(a,3246))},3246:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>f});var t=a(5155),l=a(2115),r=a(6874),i=a.n(r),n=a(6681),o=a(7460),c=a(3592),d=a(12),m=a(3004),x=a(8647),u=a(4752),h=a.n(u);function f(){var e;let{user:s,loading:r}=(0,n.hD)(),{hasBlockingNotifications:u,isChecking:f,markAllAsRead:w}=(0,o.J)((null==s?void 0:s.uid)||null);(0,l.useEffect)(()=>{r||s||(window.location.href="/login")},[s,r]);let[p,b]=(0,l.useState)(null),[g,j]=(0,l.useState)(!0),[N,v]=(0,l.useState)(!1),[y,P]=(0,l.useState)({name:"",email:"",mobile:"",currentPassword:"",newPassword:"",confirmPassword:""}),[k,C]=(0,l.useState)(!1),[E,S]=(0,l.useState)(!1),[A,F]=(0,l.useState)(!1),[D,R]=(0,l.useState)(!1),[B,L]=(0,l.useState)(!1),[M,T]=(0,l.useState)(0);(0,l.useEffect)(()=>{s&&U()},[s]);let U=async()=>{try{j(!0);let e=await (0,c.getUserData)(s.uid);if(b(e),P({name:(null==e?void 0:e.name)||"",email:(null==e?void 0:e.email)||"",mobile:(null==e?void 0:e.mobile)||"",currentPassword:"",newPassword:"",confirmPassword:""}),e)try{let{updateUserActiveDays:e,isUserPlanExpired:t}=await Promise.resolve().then(a.bind(a,3592));await e(s.uid);let l=await (0,c.getUserData)(s.uid);b(l);let r=await t(s.uid);T(r.activeDays||0),P({name:(null==l?void 0:l.name)||"",email:(null==l?void 0:l.email)||"",mobile:(null==l?void 0:l.mobile)||"",currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){console.error("Error updating active days:",e)}}catch(e){console.error("Error loading user data:",e),h().fire({icon:"error",title:"Error",text:"Failed to load profile data. Please try again."})}finally{j(!1)}},V=async()=>{if(!y.name.trim())return void h().fire({icon:"error",title:"Validation Error",text:"Name is required"});if(y.mobile&&!/^[6-9]\d{9}$/.test(y.mobile))return void h().fire({icon:"error",title:"Validation Error",text:"Please enter a valid 10-digit mobile number"});if(y.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(y.email))return void h().fire({icon:"error",title:"Validation Error",text:"Please enter a valid email address"});if(B){if(!y.currentPassword)return void h().fire({icon:"error",title:"Validation Error",text:"Current password is required to change password"});if(!y.newPassword||y.newPassword.length<6)return void h().fire({icon:"error",title:"Validation Error",text:"New password must be at least 6 characters long"});if(y.newPassword!==y.confirmPassword)return void h().fire({icon:"error",title:"Validation Error",text:"New password and confirm password do not match"})}try{if(C(!0),B&&y.currentPassword&&y.newPassword)try{let e=m.IX.credential(s.email,y.currentPassword);await (0,m.kZ)(s,e),await (0,m.f3)(s,y.newPassword),h().fire({icon:"success",title:"Password Updated",text:"Your password has been updated successfully",timer:2e3,showConfirmButton:!1})}catch(s){console.error("Error updating password:",s);let e="Failed to update password. Please try again.";"auth/wrong-password"===s.code?e="Current password is incorrect":"auth/too-many-requests"===s.code&&(e="Too many failed attempts. Please try again later."),h().fire({icon:"error",title:"Password Update Failed",text:e});return}if(y.email!==(null==p?void 0:p.email)&&y.email)try{await (0,m.Ww)(s,y.email)}catch(s){console.error("Error updating email:",s);let e="Failed to update email. Please try again.";"auth/email-already-in-use"===s.code?e="This email is already in use by another account":"auth/requires-recent-login"===s.code&&(e="Please log out and log back in before changing your email"),h().fire({icon:"error",title:"Email Update Failed",text:e});return}let e={name:y.name.trim(),mobile:y.mobile};y.email!==(null==p?void 0:p.email)&&y.email&&(e.email=y.email),await (0,c.b6)(s.uid,e),b(s=>s?{...s,...e}:null),v(!1),L(!1),h().fire({icon:"success",title:"Profile Updated",text:"Your profile has been updated successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error updating profile:",e),h().fire({icon:"error",title:"Update Failed",text:"Failed to update profile. Please try again."})}finally{C(!1)}};return r||g||f?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"spinner mb-4"}),(0,t.jsx)("p",{className:"text-white",children:r?"Loading...":f?"Checking notifications...":"Loading profile..."})]})}):u&&s?(0,t.jsx)(x.A,{userId:s.uid,onAllRead:w}):(0,t.jsxs)("div",{className:"min-h-screen p-4",children:[(0,t.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(i(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,t.jsx)("h1",{className:"text-xl font-bold text-white",children:"My Profile"}),(0,t.jsxs)("button",{onClick:()=>{(0,d._f)(null==s?void 0:s.uid,"/login")},className:"glass-button px-4 py-2 text-white",children:[(0,t.jsx)("i",{className:"fas fa-sign-out-alt mr-2"}),"Logout"]})]})}),(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,t.jsx)("i",{className:"fas fa-user mr-2"}),"Profile Information"]}),!N&&(0,t.jsxs)("button",{onClick:()=>{v(!0)},className:"glass-button px-4 py-2 text-white",children:[(0,t.jsx)("i",{className:"fas fa-edit mr-2"}),"Edit"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Full Name"}),N?(0,t.jsx)("input",{type:"text",value:y.name,onChange:e=>P(s=>({...s,name:e.target.value})),className:"form-input",placeholder:"Enter your full name"}):(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:(null==p?void 0:p.name)||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Email Address"}),N?(0,t.jsx)("input",{type:"email",value:y.email,onChange:e=>P(s=>({...s,email:e.target.value})),className:"form-input",placeholder:"Enter your email address"}):(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:(null==p?void 0:p.email)||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Mobile Number"}),N?(0,t.jsx)("input",{type:"tel",value:y.mobile,onChange:e=>P(s=>({...s,mobile:e.target.value})),className:"form-input",placeholder:"Enter 10-digit mobile number",maxLength:10}):(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:(null==p?void 0:p.mobile)||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Member Since"}),(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:(null==p||null==(e=p.joinedDate)?void 0:e.toLocaleDateString())||"Unknown"})]}),N&&(0,t.jsxs)("div",{className:"border-t border-white/20 pt-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Change Password"}),(0,t.jsxs)("button",{onClick:()=>L(!B),className:"glass-button px-4 py-2 text-white ".concat(B?"bg-red-500/20":"bg-blue-500/20"),children:[(0,t.jsx)("i",{className:"fas ".concat(B?"fa-times":"fa-key"," mr-2")}),B?"Cancel":"Change Password"]})]}),B&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Current Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:E?"text":"password",value:y.currentPassword,onChange:e=>P(s=>({...s,currentPassword:e.target.value})),className:"form-input pr-12",placeholder:"Enter your current password"}),(0,t.jsx)("button",{type:"button",onClick:()=>S(!E),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,t.jsx)("i",{className:"fas ".concat(E?"fa-eye-slash":"fa-eye")})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:A?"text":"password",value:y.newPassword,onChange:e=>P(s=>({...s,newPassword:e.target.value})),className:"form-input pr-12",placeholder:"Enter new password (min 6 characters)"}),(0,t.jsx)("button",{type:"button",onClick:()=>F(!A),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,t.jsx)("i",{className:"fas ".concat(A?"fa-eye-slash":"fa-eye")})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Confirm New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:D?"text":"password",value:y.confirmPassword,onChange:e=>P(s=>({...s,confirmPassword:e.target.value})),className:"form-input pr-12",placeholder:"Confirm your new password"}),(0,t.jsx)("button",{type:"button",onClick:()=>R(!D),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,t.jsx)("i",{className:"fas ".concat(D?"fa-eye-slash":"fa-eye")})})]})]}),(0,t.jsx)("div",{className:"bg-yellow-500/20 p-3 rounded-lg",children:(0,t.jsxs)("p",{className:"text-yellow-300 text-sm",children:[(0,t.jsx)("i",{className:"fas fa-exclamation-triangle mr-2"}),"Password must be at least 6 characters long. You will need to log in again after changing your password."]})})]})]})]}),N&&(0,t.jsxs)("div",{className:"flex gap-4 mt-6",children:[(0,t.jsx)("button",{onClick:V,disabled:k,className:"btn-primary flex-1",children:k?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-save mr-2"}),"Save Changes"]})}),(0,t.jsxs)("button",{onClick:()=>{v(!1),L(!1),P({name:(null==p?void 0:p.name)||"",email:(null==p?void 0:p.email)||"",mobile:(null==p?void 0:p.mobile)||"",currentPassword:"",newPassword:"",confirmPassword:""})},className:"btn-secondary flex-1",children:[(0,t.jsx)("i",{className:"fas fa-times mr-2"}),"Cancel"]})]})]}),(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-crown mr-2"}),"Plan Information"]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Current Plan"}),(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:(null==p?void 0:p.plan)||"Trial"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Active Days"}),(0,t.jsxs)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:[M," days"]})]}),(null==p?void 0:p.planExpiry)&&(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Plan Expires"}),(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:p.planExpiry.toLocaleDateString()})]})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsxs)(i(),{href:"/plans",className:"btn-primary",children:[(0,t.jsx)("i",{className:"fas fa-upgrade mr-2"}),"Upgrade Plan"]})})]}),(0,t.jsxs)("div",{className:"glass-card p-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-users mr-2"}),"Referral Information"]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Your Referral Code"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg flex-1 font-mono",children:(null==p?void 0:p.referralCode)||"Not generated"}),(0,t.jsx)("button",{onClick:()=>{(null==p?void 0:p.referralCode)&&(navigator.clipboard.writeText(p.referralCode),h().fire({icon:"success",title:"Copied!",text:"Referral code copied to clipboard",timer:1500,showConfirmButton:!1}))},className:"glass-button px-4 py-2 text-white",title:"Copy referral code",children:(0,t.jsx)("i",{className:"fas fa-copy"})})]})]}),(null==p?void 0:p.referredBy)&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Referred By"}),(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg font-mono",children:p.referredBy})]}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)("button",{onClick:()=>{if(null==p?void 0:p.referralCode){let e="".concat(window.location.origin,"/register?ref=").concat(p.referralCode);navigator.share?navigator.share({title:"Join MyTube and Start Earning",text:"Join MyTube using my referral code and start earning money by watching videos!",url:e}):(navigator.clipboard.writeText(e),h().fire({icon:"success",title:"Link Copied!",text:"Referral link copied to clipboard",timer:2e3,showConfirmButton:!1}))}},className:"btn-primary flex-1",children:[(0,t.jsx)("i",{className:"fas fa-share mr-2"}),"Share Referral Link"]}),(0,t.jsxs)(i(),{href:"/refer",className:"btn-secondary flex-1 text-center",children:[(0,t.jsx)("i",{className:"fas fa-users mr-2"}),"View Referrals"]})]})]})]})]})}},7460:(e,s,a)=>{"use strict";a.d(s,{J:()=>r});var t=a(2115),l=a(3592);function r(e){let[s,a]=(0,t.useState)(!1),[r,i]=(0,t.useState)(!0);(0,t.useEffect)(()=>{e?n():i(!1)},[e]);let n=async()=>{try{i(!0);let s=await (0,l.iA)(e);a(s)}catch(e){console.error("Error checking for blocking notifications:",e),a(!1)}finally{i(!1)}};return{hasBlockingNotifications:s,isChecking:r,checkForBlockingNotifications:n,markAllAsRead:()=>{a(!1)}}}},8647:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});var t=a(5155),l=a(2115),r=a(3592);function i(e){let{userId:s,onAllRead:a}=e,[i,n]=(0,l.useState)([]),[o,c]=(0,l.useState)(0),[d,m]=(0,l.useState)(!0);(0,l.useEffect)(()=>{s&&x()},[s]);let x=async()=>{try{m(!0);let e=await (0,r.AX)(s);n(e),0===e.length&&a()}catch(e){console.error("Error loading notifications:",e),a()}finally{m(!1)}},u=async()=>{let e=i[o];(null==e?void 0:e.id)&&(await (0,r.bA)(e.id,s),o<i.length-1?c(o+1):a())};if(d)return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,t.jsx)("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})})});if(0===i.length)return null;let h=i[o];return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("i",{className:(e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}})(h.type)}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-bold",children:"Important Notice"}),(0,t.jsxs)("p",{className:"text-blue-100 text-sm",children:[o+1," of ",i.length," notifications"]})]})]}),(0,t.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full px-3 py-1",children:(0,t.jsx)("span",{className:"text-sm font-medium",children:"Required"})})]})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:h.title}),(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,t.jsx)("p",{className:"text-gray-800 leading-relaxed",children:h.message})}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-6",children:[(0,t.jsxs)("span",{children:["From: ",h.createdBy]}),(0,t.jsx)("span",{children:(e=>{let s=Math.floor((new Date().getTime()-e.getTime())/1e3);return s<60?"Just now":s<3600?"".concat(Math.floor(s/60)," minutes ago"):s<86400?"".concat(Math.floor(s/3600)," hours ago"):"".concat(Math.floor(s/86400)," days ago")})(h.createdAt)})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,t.jsx)("span",{children:"Progress"}),(0,t.jsxs)("span",{children:[o+1,"/",i.length]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat((o+1)/i.length*100,"%")}})})]}),(0,t.jsxs)("button",{onClick:u,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,t.jsx)("i",{className:"fas fa-check"}),(0,t.jsx)("span",{children:o<i.length-1?"Acknowledge & Continue":"Acknowledge & Proceed"})]})]}),(0,t.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-t",children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,t.jsx)("i",{className:"fas fa-info-circle"}),(0,t.jsx)("span",{children:"You must acknowledge all notifications to continue"})]})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,5181,6874,3592,6681,8441,1684,7358],()=>s(1510)),_N_E=e.O()}]);