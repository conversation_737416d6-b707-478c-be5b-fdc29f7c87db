(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1730],{4732:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>h});var s=t(5155),l=t(2115),i=t(6874),n=t.n(i),r=t(6681),c=t(7460),d=t(6572),o=t(3592),m=t(8647),x=t(4752),u=t.n(x);function h(){let{user:e,loading:a}=(0,r.Nu)(),{hasBlockingNotifications:t,isChecking:i,markAllAsRead:x}=(0,c.J)((null==e?void 0:e.uid)||null),{isBlocked:h,leaveStatus:b}=(0,d.l)({userId:(null==e?void 0:e.uid)||null,checkInterval:3e4,enabled:!!e}),[f,w]=(0,l.useState)(null),[N,j]=(0,l.useState)([]),[p,v]=(0,l.useState)(!0),[g,y]=(0,l.useState)(""),[k,C]=(0,l.useState)(!1),[S,E]=(0,l.useState)(null),[W,A]=(0,l.useState)(!1),[B,F]=(0,l.useState)({accountHolderName:"",accountNumber:"",ifscCode:"",bankName:""}),[P,D]=(0,l.useState)(!1),[q,I]=(0,l.useState)({allowed:!0}),[M,T]=(0,l.useState)(!1),[H,R]=(0,l.useState)(null);(0,l.useEffect)(()=>{e&&(Y(),O(),_(),L())},[e]),(0,l.useEffect)(()=>{h?I({allowed:!1,reason:b.reason||"Withdrawals are not available due to leave."}):e&&L()},[h,b,e]);let _=async()=>{try{let a=await (0,o.getUserData)(e.uid);R(a)}catch(e){console.error("Error loading user data:",e)}},L=async()=>{if(e)try{T(!0);let a=await (0,o.QD)(e.uid);I(a)}catch(e){console.error("Error checking withdrawal eligibility:",e),I({allowed:!1,reason:"Unable to verify withdrawal eligibility. Please try again."})}finally{T(!1)}},Y=async()=>{try{v(!0);let[a,t]=await Promise.all([(0,o.getWalletData)(e.uid),(0,o.i8)(e.uid,20)]);w(a);let s=t.map(e=>({id:e.id,type:"withdrawal",amount:-e.amount,description:"Withdrawal request - ₹".concat(e.amount),date:e.date,status:e.status}));j(s)}catch(e){console.error("Error loading wallet data:",e),u().fire({icon:"error",title:"Error",text:"Failed to load wallet data. Please try again."})}finally{v(!1)}},O=async()=>{try{let a=await (0,o.zb)(e.uid);E(a),a&&F(a)}catch(e){console.error("Error loading bank details:",e)}},U=async a=>{if(a.preventDefault(),!P)try{D(!0),await (0,o.mm)(e.uid,B),E(B),A(!1),u().fire({icon:"success",title:"Bank Details Saved",text:"Your bank details have been saved successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error saving bank details:",e),u().fire({icon:"error",title:"Error",text:e.message||"Failed to save bank details. Please try again."})}finally{D(!1)}},z=e=>{let{name:a,value:t}=e.target;F(e=>({...e,[a]:t}))},J=async()=>{if(k)return;if(h)return void u().fire({icon:"warning",title:"Withdrawal Not Available",text:b.reason||"Withdrawals are not available due to leave.",confirmButtonText:"OK"});let a=parseFloat(g);if(!a||a<=0)return void u().fire({icon:"error",title:"Invalid Amount",text:"Please enter a valid amount to withdraw"});if(a<50)return void u().fire({icon:"error",title:"Minimum Withdrawal",text:"Minimum withdrawal amount is ₹50"});if(a>((null==f?void 0:f.wallet)||0))return void u().fire({icon:"error",title:"Insufficient Balance",text:"You do not have enough balance in your wallet"});if(!S)return void u().fire({icon:"warning",title:"Bank Details Required",text:"Please add your bank details before making a withdrawal"});try{C(!0),await (0,o.xj)(e.uid,a,S),await Y(),u().fire({icon:"success",title:"Withdrawal Request Submitted",text:"Your withdrawal request for ₹".concat(a," has been submitted and will be processed within 24-48 hours.")}),y("")}catch(e){console.error("Error processing withdrawal:",e),u().fire({icon:"error",title:"Withdrawal Failed",text:e.message||"Failed to process withdrawal request. Please try again."})}finally{C(!1)}},K=e=>null==e||isNaN(e)?"₹0.00":"₹".concat(e.toFixed(2));return a||p||i?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner mb-4"}),(0,s.jsx)("p",{className:"text-white",children:a?"Loading...":i?"Checking notifications...":"Loading wallet..."})]})}):t&&e?(0,s.jsx)(m.A,{userId:e.uid,onAllRead:x}):(0,s.jsxs)("div",{className:"min-h-screen p-4",children:[(0,s.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(n(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"My Wallet"}),(0,s.jsxs)("button",{onClick:Y,className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"text-2xl font-semibold text-white",children:"My Wallet"}),(0,s.jsx)("i",{className:"fas fa-wallet text-green-400 text-3xl"})]}),(0,s.jsx)("p",{className:"text-4xl font-bold text-green-400 mb-2",children:K((null==f?void 0:f.wallet)||0)}),(0,s.jsx)("p",{className:"text-white/60",children:"Total available balance"})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,s.jsx)("i",{className:"fas fa-university mr-2"}),"Bank Details"]}),S&&!W&&(0,s.jsxs)("button",{onClick:()=>A(!0),className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-edit mr-2"}),"Edit"]})]}),S||W?W?(0,s.jsxs)("form",{onSubmit:U,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Account Holder Name *"}),(0,s.jsx)("input",{type:"text",name:"accountHolderName",value:B.accountHolderName,onChange:z,className:"form-input",placeholder:"Enter account holder name",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Bank Name *"}),(0,s.jsx)("input",{type:"text",name:"bankName",value:B.bankName,onChange:z,className:"form-input",placeholder:"Enter bank name",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Account Number *"}),(0,s.jsx)("input",{type:"text",name:"accountNumber",value:B.accountNumber,onChange:z,className:"form-input",placeholder:"Enter account number",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"IFSC Code *"}),(0,s.jsx)("input",{type:"text",name:"ifscCode",value:B.ifscCode,onChange:z,className:"form-input",placeholder:"Enter IFSC code (e.g., SBIN0001234)",required:!0})]})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)("button",{type:"submit",disabled:P,className:"".concat(P?"btn-disabled cursor-not-allowed opacity-50":"btn-primary hover:bg-blue-600"),children:P?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Saving Bank Details..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-save mr-2"}),"Save Bank Details"]})}),(0,s.jsx)("button",{type:"button",onClick:()=>A(!1),className:"btn-secondary",children:"Cancel"})]})]}):(0,s.jsx)("div",{className:"bg-white/10 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"Account Holder"}),(0,s.jsx)("p",{className:"text-white font-medium",children:null==S?void 0:S.accountHolderName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"Bank Name"}),(0,s.jsx)("p",{className:"text-white font-medium",children:null==S?void 0:S.bankName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"Account Number"}),(0,s.jsxs)("p",{className:"text-white font-medium",children:["****",null==S?void 0:S.accountNumber.slice(-4)]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"IFSC Code"}),(0,s.jsx)("p",{className:"text-white font-medium",children:null==S?void 0:S.ifscCode})]})]})}):(0,s.jsxs)("div",{className:"text-center py-6",children:[(0,s.jsx)("i",{className:"fas fa-university text-white/30 text-4xl mb-4"}),(0,s.jsx)("p",{className:"text-white/60 mb-4",children:"No bank details added yet"}),(0,s.jsxs)("button",{onClick:()=>A(!0),className:"btn-primary",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Add Bank Details"]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Withdraw Funds"]}),(null==H?void 0:H.plan)==="Trial"&&(0,s.jsxs)("div",{className:"mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)("i",{className:"fas fa-lock text-red-400 mr-2"}),(0,s.jsx)("span",{className:"text-red-400 font-medium",children:"Trial Plan Restriction"})]}),(0,s.jsx)("p",{className:"text-white/80 text-sm mb-3",children:"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawal functionality."}),(0,s.jsx)("div",{className:"flex gap-3",children:(0,s.jsxs)(n(),{href:"/plans",className:"btn-primary inline-block",children:[(0,s.jsx)("i",{className:"fas fa-arrow-up mr-2"}),"Upgrade Plan"]})})]}),(0,s.jsxs)("div",{className:"mb-4 p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)("i",{className:"fas fa-clock text-blue-400 mr-2"}),(0,s.jsx)("span",{className:"text-blue-400 font-medium",children:"Withdrawal Timings"})]}),(0,s.jsxs)("p",{className:"text-white/80 text-sm mb-2",children:["Withdrawals are only allowed between ",(0,s.jsx)("strong",{children:"10:00 AM to 6:00 PM"})," on non-leave days."]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("p",{className:"text-white/60 text-xs",children:["Current time: ",new Date().toLocaleTimeString()," | Status: ",q.allowed?(0,s.jsx)("span",{className:"text-green-400 font-medium",children:"✓ Available"}):(0,s.jsx)("span",{className:"text-red-400 font-medium",children:"✗ Not Available"})]}),(0,s.jsx)("button",{onClick:L,disabled:M,className:"text-blue-400 hover:text-blue-300 text-xs",children:M?(0,s.jsx)("div",{className:"spinner w-3 h-3"}):(0,s.jsx)("i",{className:"fas fa-sync-alt"})})]}),!q.allowed&&q.reason&&(0,s.jsxs)("p",{className:"text-red-400 text-sm mt-2",children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),q.reason]})]}),S?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,s.jsx)("input",{type:"number",value:g,onChange:e=>y(e.target.value),placeholder:"Enter amount to withdraw (Min: ₹50)",className:"form-input flex-1",min:"50",max:(null==f?void 0:f.wallet)||0}),(0,s.jsx)("button",{onClick:J,disabled:k||!g||!q.allowed,className:"whitespace-nowrap ".concat(k?"btn-disabled cursor-not-allowed opacity-50":q.allowed&&g?"btn-primary hover:bg-blue-600":"btn-disabled cursor-not-allowed opacity-50"),children:k?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Processing Withdrawal..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Withdraw ₹",g||"0"]})})]}),(0,s.jsxs)("p",{className:"text-white/60 text-sm mt-2",children:["Available: ",K((null==f?void 0:f.wallet)||0)," | Minimum: ₹50"]})]}):(0,s.jsxs)("div",{className:"text-center py-6",children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle text-yellow-400 text-3xl mb-4"}),(0,s.jsx)("p",{className:"text-white/60 mb-4",children:"Please add your bank details before making a withdrawal"}),(0,s.jsxs)("button",{onClick:()=>A(!0),className:"btn-primary",children:[(0,s.jsx)("i",{className:"fas fa-university mr-2"}),"Add Bank Details"]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,s.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Withdrawal History"]}),(0,s.jsxs)("button",{onClick:Y,className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]}),0===N.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("i",{className:"fas fa-money-bill-wave text-white/30 text-4xl mb-4"}),(0,s.jsx)("p",{className:"text-white/60 mb-2",children:"No withdrawal requests yet"}),(0,s.jsx)("p",{className:"text-white/40 text-sm",children:"Your withdrawal requests will appear here"})]}):(0,s.jsx)("div",{className:"space-y-3",children:N.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/10 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-money-bill-wave text-red-400 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white font-medium",children:e.description}),(0,s.jsxs)("p",{className:"text-white/60 text-sm",children:[e.date.toLocaleDateString()," at ",e.date.toLocaleTimeString()]})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("p",{className:"font-bold text-red-400",children:K(Math.abs(e.amount))}),(0,s.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat("pending"===e.status?"bg-yellow-500/20 text-yellow-400":"approved"===e.status?"bg-green-500/20 text-green-400":"rejected"===e.status?"bg-red-500/20 text-red-400":"completed"===e.status?"bg-blue-500/20 text-blue-400":"bg-gray-500/20 text-gray-400"),children:"pending"===e.status?"⏳ Pending":"approved"===e.status?"✅ Approved":"rejected"===e.status?"❌ Rejected":"completed"===e.status?"✅ Completed":e.status})]})]},e.id))})]})]})}},6034:(e,a,t)=>{Promise.resolve().then(t.bind(t,4732))}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,8320,5181,6874,3592,6681,3499,8441,1684,7358],()=>a(6034)),_N_E=e.O()}]);