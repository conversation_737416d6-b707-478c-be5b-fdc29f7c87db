"use strict";exports.id=6951,exports.ids=[6951,7087],exports.modules={744:(e,t,a)=>{a.d(t,{J:()=>s});var r=a(43210),o=a(3582);function s(e){let[t,a]=(0,r.useState)(!1),[s,n]=(0,r.useState)(!0);return{hasBlockingNotifications:t,isChecking:s,checkForBlockingNotifications:async()=>{try{n(!0);let t=await (0,o.iA)(e);a(t)}catch(e){console.error("Error checking for blocking notifications:",e),a(!1)}finally{n(!1)}},markAllAsRead:()=>{a(!1)}}}},55986:(e,t,a)=>{a.d(t,{l:()=>s});var r=a(43210),o=a(87087);function s({userId:e,checkInterval:t=3e4,enabled:a=!0}){let[s,n]=(0,r.useState)({blocked:!1,lastChecked:new Date}),[l,c]=(0,r.useState)(!1);return{leaveStatus:s,isChecking:l,checkLeaveStatus:(0,r.useCallback)(async()=>{if(e&&a)try{c(!0);let t=await (0,o.q8)(e);return n({blocked:t.blocked,reason:t.reason,lastChecked:new Date}),t}catch(e){return console.error("Error checking leave status:",e),n(e=>({...e,lastChecked:new Date})),{blocked:!1}}finally{c(!1)}},[e,a]),isBlocked:s.blocked}}},87087:(e,t,a)=>{a.d(t,{applyUserLeave:()=>u,cancelUserLeave:()=>m,createAdminLeave:()=>n,debugAdminLeaveStatus:()=>c,deleteAdminLeave:()=>i,getAdminLeaves:()=>l,getUserLeaves:()=>g,getUserMonthlyLeaveCount:()=>h,q8:()=>v});var r=a(33784),o=a(75535);let s={adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function n(e){try{return(await (0,o.gS)((0,o.collection)(r.db,s.adminLeaves),{...e,date:o.Dc.fromDate(e.date),createdAt:o.Dc.now()})).id}catch(e){throw console.error("Error creating admin leave:",e),e}}async function l(){try{let e=(0,o.P)((0,o.collection)(r.db,s.adminLeaves),(0,o.My)("date","asc")),t=(await (0,o.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),createdAt:e.data().createdAt.toDate()}));return console.log("\uD83D\uDCC5 All admin leaves:",t),t}catch(e){throw console.error("Error getting admin leaves:",e),e}}async function c(){try{let e=new Date;console.log("\uD83D\uDD0D Debug: Checking admin leave status for today:",e.toDateString());let t=await d(e);console.log("\uD83D\uDCCA Debug: Admin leave result:",t);let a=await l();console.log("\uD83D\uDCC5 Debug: All admin leaves in database:",a);let r=a.filter(t=>t.date.toDateString()===e.toDateString());console.log("\uD83D\uDCC5 Debug: Today's admin leaves:",r)}catch(e){console.error("❌ Debug: Error checking admin leave status:",e)}}async function i(e){try{await (0,o.kd)((0,o.H9)(r.db,s.adminLeaves,e))}catch(e){throw console.error("Error deleting admin leave:",e),e}}async function d(e){try{let t=new Date(e);t.setHours(0,0,0,0);let a=new Date(e);a.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking admin leave for date range:",t.toISOString(),"to",a.toISOString());let n=(0,o.P)((0,o.collection)(r.db,s.adminLeaves),(0,o._M)("date",">=",o.Dc.fromDate(t)),(0,o._M)("date","<=",o.Dc.fromDate(a))),l=await (0,o.getDocs)(n),c=!l.empty;return c?console.log("\uD83D\uDCC5 Found admin leave(s) for today:",l.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDCC5 No admin leaves found for today"),c}catch(e){return console.error("❌ Error checking admin leave day:",e),!1}}async function u(e){try{let t,a,n,l=new Date,c=l.getFullYear(),i=l.getMonth()+1,d=await h(e.userId,c,i),u="pending";return d<4&&(u="approved",t="system",n=o.Dc.now(),a=`Auto-approved: ${d+1}/4 monthly leaves used`),{id:(await (0,o.gS)((0,o.collection)(r.db,s.userLeaves),{...e,date:o.Dc.fromDate(e.date),status:u,appliedAt:o.Dc.now(),...t&&{reviewedBy:t},...n&&{reviewedAt:n},...a&&{reviewNotes:a}})).id,autoApproved:"approved"===u,usedLeaves:d+ +("approved"===u),maxLeaves:4}}catch(e){throw console.error("Error applying user leave:",e),e}}async function g(e){try{let t=(0,o.P)((0,o.collection)(r.db,s.userLeaves),(0,o._M)("userId","==",e),(0,o.My)("date","desc"));return(await (0,o.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),appliedAt:e.data().appliedAt.toDate(),reviewedAt:e.data().reviewedAt?.toDate()}))}catch(e){throw console.error("Error getting user leaves:",e),e}}async function m(e){try{await (0,o.kd)((0,o.H9)(r.db,s.userLeaves,e))}catch(e){throw console.error("Error cancelling user leave:",e),e}}async function h(e,t,a){try{let n=new Date(t,a-1,1),l=new Date(t,a,0,23,59,59,999),c=(0,o.P)((0,o.collection)(r.db,s.userLeaves),(0,o._M)("userId","==",e),(0,o._M)("status","==","approved"),(0,o._M)("date",">=",o.Dc.fromDate(n)),(0,o._M)("date","<=",o.Dc.fromDate(l)));return(await (0,o.getDocs)(c)).size}catch(e){return console.error("Error getting user monthly leave count:",e),0}}async function f(e,t){try{let a=new Date(t);a.setHours(0,0,0,0);let n=new Date(t);n.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking user leave for user:",e,"on date range:",a.toISOString(),"to",n.toISOString());let l=(0,o.P)((0,o.collection)(r.db,s.userLeaves),(0,o._M)("userId","==",e),(0,o._M)("status","==","approved"),(0,o._M)("date",">=",o.Dc.fromDate(a)),(0,o._M)("date","<=",o.Dc.fromDate(n))),c=await (0,o.getDocs)(l),i=!c.empty;return i?console.log("\uD83D\uDC64 Found user leave(s) for today:",c.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDC64 No user leaves found for today"),i}catch(e){return console.error("❌ Error checking user leave day:",e),!1}}async function v(e){try{let t=new Date;console.log("\uD83D\uDD0D Checking work block status for user:",e,"on date:",t.toDateString());try{let e=await d(t);if(console.log("\uD83D\uDCC5 Admin leave check result:",e),e)return console.log("\uD83D\uDEAB Work blocked due to admin leave"),{blocked:!0,reason:"System maintenance/holiday"}}catch(e){console.error("❌ Error checking admin leave (allowing work to continue):",e)}try{let a=await f(e,t);if(console.log("\uD83D\uDC64 User leave check result:",a),a)return console.log("\uD83D\uDEAB Work blocked due to user leave"),{blocked:!0,reason:"You are on approved leave today"}}catch(e){console.error("❌ Error checking user leave (allowing work to continue):",e)}return console.log("✅ Work is not blocked"),{blocked:!1}}catch(e){return console.error("❌ Error checking work block status (allowing work to continue):",e),{blocked:!1}}}},98873:(e,t,a)=>{a.d(t,{A:()=>n});var r=a(60687),o=a(43210),s=a(3582);function n({userId:e,onAllRead:t}){let[a,n]=(0,o.useState)([]),[l,c]=(0,o.useState)(0),[i,d]=(0,o.useState)(!0),u=async()=>{let r=a[l];r?.id&&(await (0,s.bA)(r.id,e),l<a.length-1?c(l+1):t())};if(i)return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,r.jsx)("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})})});if(0===a.length)return null;let g=a[l];return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("i",{className:(e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}})(g.type)}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold",children:"Important Notice"}),(0,r.jsxs)("p",{className:"text-blue-100 text-sm",children:[l+1," of ",a.length," notifications"]})]})]}),(0,r.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full px-3 py-1",children:(0,r.jsx)("span",{className:"text-sm font-medium",children:"Required"})})]})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:g.title}),(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,r.jsx)("p",{className:"text-gray-800 leading-relaxed",children:g.message})}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-6",children:[(0,r.jsxs)("span",{children:["From: ",g.createdBy]}),(0,r.jsx)("span",{children:(e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?"Just now":t<3600?`${Math.floor(t/60)} minutes ago`:t<86400?`${Math.floor(t/3600)} hours ago`:`${Math.floor(t/86400)} days ago`})(g.createdAt)})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsxs)("span",{children:[l+1,"/",a.length]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${(l+1)/a.length*100}%`}})})]}),(0,r.jsxs)("button",{onClick:u,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,r.jsx)("i",{className:"fas fa-check"}),(0,r.jsx)("span",{children:l<a.length-1?"Acknowledge & Continue":"Acknowledge & Proceed"})]})]}),(0,r.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-t",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)("i",{className:"fas fa-info-circle"}),(0,r.jsx)("span",{children:"You must acknowledge all notifications to continue"})]})})]})})}}};