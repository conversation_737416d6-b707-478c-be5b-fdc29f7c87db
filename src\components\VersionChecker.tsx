'use client'

import { useEffect, useState } from 'react'
import { checkVersionAndClearCache, manualCacheClear, simpleCacheClear, getVersionInfo } from '@/lib/versionService'

interface VersionCheckerProps {
  showClearButton?: boolean
}

export default function VersionChecker({ showClearButton = false }: VersionCheckerProps) {
  const [isChecking, setIsChecking] = useState(false)
  const [versionInfo, setVersionInfo] = useState<any>(null)

  useEffect(() => {
    // Check version on component mount
    const checkVersion = async () => {
      try {
        setIsChecking(true)
        const cacheCleared = await checkVersionAndClearCache()
        
        if (cacheCleared) {
          // If cache was cleared, reload the page to get fresh content
          setTimeout(() => {
            window.location.reload()
          }, 1000)
        }
        
        // Update version info
        setVersionInfo(getVersionInfo())
      } catch (error) {
        console.error('Error checking version:', error)
      } finally {
        setIsChecking(false)
      }
    }

    checkVersion()
  }, [])

  const handleManualClear = async () => {
    try {
      // Show confirmation first
      const confirmed = confirm(
        'This will clear all cached data and reload the page to get the latest version. Continue?'
      )

      if (!confirmed) return

      // Try the full cache clear with timeout
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Timeout')), 10000)
      )

      try {
        await Promise.race([manualCacheClear(), timeoutPromise])
      } catch (error) {
        console.error('Cache clear failed, trying simple clear:', error)

        // Fallback: simple cache clear
        simpleCacheClear()
        alert('Cache cleared! The page will now reload.')
        window.location.reload()
      }
    } catch (error) {
      console.error('Error during manual cache clear:', error)

      // Last resort: just reload
      window.location.reload()
    }
  }

  // Don't render anything if not showing the clear button
  if (!showClearButton) {
    return null
  }

  return (
    <div className="version-checker">
      {showClearButton && (
        <button
          onClick={handleManualClear}
          className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm transition-colors duration-200 flex items-center gap-2"
          title="Clear cache and data to get the latest version"
        >
          <i className="fas fa-sync-alt"></i>
          Clear Cache & Data
        </button>
      )}
      
      {isChecking && (
        <div className="text-xs text-gray-500 mt-1">
          Checking for updates...
        </div>
      )}
      
      {versionInfo && (
        <div className="text-xs text-gray-400 mt-1">
          v{versionInfo.currentVersion}
        </div>
      )}
    </div>
  )
}
