{"/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/leaves/page": "/admin/leaves", "/_not-found/page": "/_not-found", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/login/page": "/admin/login", "/admin/notifications/page": "/admin/notifications", "/admin/page": "/admin", "/admin/setup/page": "/admin/setup", "/admin/settings/page": "/admin/settings", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/upload-users/page": "/admin/upload-users", "/admin/users/page": "/admin/users", "/clear-cache/page": "/clear-cache", "/admin/transactions/page": "/admin/transactions", "/admin/withdrawals/page": "/admin/withdrawals", "/dashboard/page": "/dashboard", "/debug-firestore/page": "/debug-firestore", "/debug-firestore-issue/page": "/debug-firestore-issue", "/admin/simple-upload/page": "/admin/simple-upload", "/debug-registration-simple/page": "/debug-registration-simple", "/debug-registration/page": "/debug-registration", "/login/page": "/login", "/forgot-password/page": "/forgot-password", "/page": "/", "/plans/page": "/plans", "/refer/page": "/refer", "/profile/page": "/profile", "/register/page": "/register", "/registration-diagnostics/page": "/registration-diagnostics", "/support/page": "/support", "/reset-password/page": "/reset-password", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firebase/page": "/test-firebase", "/test-firestore/page": "/test-firestore", "/test-reg-simple/page": "/test-reg-simple", "/test-simple-registration/page": "/test-simple-registration", "/test-registration/page": "/test-registration", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/work/page": "/work"}