{"/_not-found/page": "/_not-found", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/login/page": "/admin/login", "/admin/leaves/page": "/admin/leaves", "/admin/setup/page": "/admin/setup", "/admin/notifications/page": "/admin/notifications", "/admin/page": "/admin", "/admin/transactions/page": "/admin/transactions", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/settings/page": "/admin/settings", "/admin/users/page": "/admin/users", "/clear-cache/page": "/clear-cache", "/admin/fix-permissions/page": "/admin/fix-permissions", "/debug-firestore-issue/page": "/debug-firestore-issue", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-firestore/page": "/debug-firestore", "/debug-registration-simple/page": "/debug-registration-simple", "/admin/upload-users/page": "/admin/upload-users", "/login/page": "/login", "/dashboard/page": "/dashboard", "/debug-registration/page": "/debug-registration", "/register/page": "/register", "/refer/page": "/refer", "/plans/page": "/plans", "/profile/page": "/profile", "/forgot-password/page": "/forgot-password", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase-connection/page": "/test-firebase-connection", "/support/page": "/support", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/reset-password/page": "/reset-password", "/test-registration/page": "/test-registration", "/test-firestore/page": "/test-firestore", "/test-simple-registration/page": "/test-simple-registration", "/page": "/", "/test-videos/page": "/test-videos", "/wallet/page": "/wallet", "/work/page": "/work", "/transactions/page": "/transactions", "/test-firebase/page": "/test-firebase", "/test-reg-simple/page": "/test-reg-simple"}