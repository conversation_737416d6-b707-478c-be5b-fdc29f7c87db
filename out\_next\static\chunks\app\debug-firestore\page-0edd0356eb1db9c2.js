(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5338],{4807:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(5155),c=s(2115),n=s(3004),i=s(5317),o=s(6104),r=s(3592);function l(){let[e,t]=(0,c.useState)(""),[s,l]=(0,c.useState)(!1),d=e=>{t(t=>t+e+"\n"),console.log(e)},u=async()=>{l(!0),t("");try{var e;d("\uD83D\uDD0D Starting Firestore Diagnostics..."),d("=".repeat(50)),d("\n\uD83D\uDCCB Test 1: Firebase Configuration"),d("Project ID: ".concat("mytube-india")),d("Auth Domain: ".concat("mytube-india.firebaseapp.com")),d("API Key: ".concat((e="AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",void 0===e)?void 0:e.substring(0,10),"...")),d("\n\uD83D\uDD17 Test 2: Firestore Connection");try{(0,i.collection)(o.db,"test"),d("✅ Firestore connection established")}catch(e){d("❌ Firestore connection failed: ".concat(e.message));return}d("\n\uD83D\uDD10 Test 3: Authentication Test");let t="test-".concat(Date.now(),"@example.com"),s=null;try{s=(await (0,n.eJ)(o.j2,t,"test123456")).user,d("✅ Test user created: ".concat(s.uid))}catch(e){d("❌ User creation failed: ".concat(e.message));return}d("\n\uD83D\uDCDD Test 4: Firestore Write Test");let a={[r.FIELD_NAMES.name]:"Debug Test User",[r.FIELD_NAMES.email]:t,[r.FIELD_NAMES.mobile]:"9999999999",[r.FIELD_NAMES.referralCode]:"DEBUG001",[r.FIELD_NAMES.referredBy]:"",[r.FIELD_NAMES.plan]:"Trial",[r.FIELD_NAMES.planExpiry]:null,[r.FIELD_NAMES.activeDays]:2,[r.FIELD_NAMES.joinedDate]:i.Dc.now(),[r.FIELD_NAMES.wallet]:0,[r.FIELD_NAMES.totalVideos]:0,[r.FIELD_NAMES.todayVideos]:0,[r.FIELD_NAMES.lastVideoDate]:null,status:"active"};try{let e=(0,i.H9)(o.db,r.COLLECTIONS.users,s.uid);d("\uD83D\uDCCD Document path: ".concat(e.path)),d("\uD83D\uDCCA Data to write: ".concat(JSON.stringify(a,null,2))),await (0,i.BN)(e,a),d("✅ Document created successfully")}catch(e){d("❌ Document creation failed: ".concat(e.message)),d("❌ Error code: ".concat(e.code)),d("❌ Full error: ".concat(JSON.stringify(e,null,2)))}d("\n\uD83D\uDCD6 Test 5: Firestore Read Test");try{let e=(0,i.H9)(o.db,r.COLLECTIONS.users,s.uid),t=await (0,i.x7)(e);t.exists()?(d("✅ Document read successfully"),d("\uD83D\uDCC4 Document data: ".concat(JSON.stringify(t.data(),null,2)))):d("❌ Document does not exist after creation")}catch(e){d("❌ Document read failed: ".concat(e.message))}d("\n\uD83D\uDCDA Test 6: Collection Access Test");try{let e=(0,i.collection)(o.db,r.COLLECTIONS.users),t=await (0,i.getDocs)(e);d("✅ Collection accessible, found ".concat(t.size," documents"))}catch(e){d("❌ Collection access failed: ".concat(e.message))}d("\n\uD83C\uDFF7️ Test 7: Field Names Check"),d("COLLECTIONS.users: ".concat(r.COLLECTIONS.users)),Object.entries(r.FIELD_NAMES).forEach(e=>{let[t,s]=e;d("FIELD_NAMES.".concat(t,": ").concat(s))}),d("\n\uD83D\uDCDD Test 8: Simple Document Test");try{let e={name:"Simple Test",email:t,created:new Date().toISOString()},a=(0,i.H9)(o.db,"test-collection",s.uid);await (0,i.BN)(a,e),d("✅ Simple document created successfully"),(await (0,i.x7)(a)).exists()?d("✅ Simple document verified"):d("❌ Simple document not found")}catch(e){d("❌ Simple document test failed: ".concat(e.message))}d("\n\uD83E\uDDF9 Cleanup: Deleting test user");try{await (0,n.hG)(s),d("✅ Test user deleted successfully")}catch(e){d("⚠️ Test user deletion failed: ".concat(e.message))}d("\n\uD83C\uDF89 Diagnostics completed!")}catch(e){d("\uD83D\uDCA5 Unexpected error: ".concat(e.message)),d("\uD83D\uDCA5 Stack trace: ".concat(e.stack))}finally{l(!1)}};return(0,a.jsx)("div",{className:"min-h-screen p-4",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"\uD83D\uDD0D Firestore Debug Tool"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"This tool will run comprehensive diagnostics to identify why Firestore user creation is failing."}),(0,a.jsx)("button",{onClick:u,disabled:s,className:"btn-primary mb-6",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Running Diagnostics..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-bug mr-2"}),"Run Firestore Diagnostics"]})}),e&&(0,a.jsxs)("div",{className:"bg-black/50 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-white font-semibold mb-2",children:"Diagnostic Results:"}),(0,a.jsx)("pre",{className:"text-green-400 text-sm whitespace-pre-wrap font-mono overflow-x-auto",children:e})]})]})})})}},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>l,j2:()=>r});var a=s(3915),c=s(3004),n=s(5317),i=s(858);let o=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),r=(0,c.xI)(o),l=(0,n.aU)(o);(0,i.c7)(o)},8820:(e,t,s)=>{Promise.resolve().then(s.bind(s,4807))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,5181,3592,8441,1684,7358],()=>t(8820)),_N_E=e.O()}]);