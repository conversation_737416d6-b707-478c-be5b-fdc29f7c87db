"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/lib/dataService.ts":
/*!********************************!*\
  !*** ./src/lib/dataService.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLLECTIONS: () => (/* binding */ COLLECTIONS),\n/* harmony export */   FIELD_NAMES: () => (/* binding */ FIELD_NAMES),\n/* harmony export */   addNotification: () => (/* binding */ addNotification),\n/* harmony export */   addTransaction: () => (/* binding */ addTransaction),\n/* harmony export */   calculateUserActiveDays: () => (/* binding */ calculateUserActiveDays),\n/* harmony export */   checkQuickVideoAdvantageActive: () => (/* binding */ checkQuickVideoAdvantageActive),\n/* harmony export */   checkWithdrawalAllowed: () => (/* binding */ checkWithdrawalAllowed),\n/* harmony export */   createWithdrawalRequest: () => (/* binding */ createWithdrawalRequest),\n/* harmony export */   dailyActiveDaysIncrement: () => (/* binding */ dailyActiveDaysIncrement),\n/* harmony export */   deleteNotification: () => (/* binding */ deleteNotification),\n/* harmony export */   fixAllUsersActiveDays: () => (/* binding */ fixAllUsersActiveDays),\n/* harmony export */   generateSequentialReferralCode: () => (/* binding */ generateSequentialReferralCode),\n/* harmony export */   generateUniqueReferralCode: () => (/* binding */ generateUniqueReferralCode),\n/* harmony export */   getAllNotifications: () => (/* binding */ getAllNotifications),\n/* harmony export */   getBankDetails: () => (/* binding */ getBankDetails),\n/* harmony export */   getPlanEarning: () => (/* binding */ getPlanEarning),\n/* harmony export */   getPlanValidityDays: () => (/* binding */ getPlanValidityDays),\n/* harmony export */   getPlanVideoDuration: () => (/* binding */ getPlanVideoDuration),\n/* harmony export */   getReferralBonus: () => (/* binding */ getReferralBonus),\n/* harmony export */   getReferrals: () => (/* binding */ getReferrals),\n/* harmony export */   getTransactions: () => (/* binding */ getTransactions),\n/* harmony export */   getUnreadNotificationCount: () => (/* binding */ getUnreadNotificationCount),\n/* harmony export */   getUnreadNotifications: () => (/* binding */ getUnreadNotifications),\n/* harmony export */   getUserData: () => (/* binding */ getUserData),\n/* harmony export */   getUserNotifications: () => (/* binding */ getUserNotifications),\n/* harmony export */   getUserVideoSettings: () => (/* binding */ getUserVideoSettings),\n/* harmony export */   getUserWithdrawals: () => (/* binding */ getUserWithdrawals),\n/* harmony export */   getVideoCountData: () => (/* binding */ getVideoCountData),\n/* harmony export */   getWalletData: () => (/* binding */ getWalletData),\n/* harmony export */   grantQuickVideoAdvantage: () => (/* binding */ grantQuickVideoAdvantage),\n/* harmony export */   hasPendingWithdrawals: () => (/* binding */ hasPendingWithdrawals),\n/* harmony export */   hasUnreadNotifications: () => (/* binding */ hasUnreadNotifications),\n/* harmony export */   isNotificationRead: () => (/* binding */ isNotificationRead),\n/* harmony export */   isUserPlanExpired: () => (/* binding */ isUserPlanExpired),\n/* harmony export */   markNotificationAsRead: () => (/* binding */ markNotificationAsRead),\n/* harmony export */   migrateQuickVideoAdvantageSystem: () => (/* binding */ migrateQuickVideoAdvantageSystem),\n/* harmony export */   processReferralBonus: () => (/* binding */ processReferralBonus),\n/* harmony export */   recalculateAllUsersActiveDays: () => (/* binding */ recalculateAllUsersActiveDays),\n/* harmony export */   removeQuickVideoAdvantage: () => (/* binding */ removeQuickVideoAdvantage),\n/* harmony export */   resetDailyVideoCount: () => (/* binding */ resetDailyVideoCount),\n/* harmony export */   saveBankDetails: () => (/* binding */ saveBankDetails),\n/* harmony export */   submitBatchVideos: () => (/* binding */ submitBatchVideos),\n/* harmony export */   updateUserActiveDays: () => (/* binding */ updateUserActiveDays),\n/* harmony export */   updateUserData: () => (/* binding */ updateUserData),\n/* harmony export */   updateUserPlanExpiry: () => (/* binding */ updateUserPlanExpiry),\n/* harmony export */   updateUserVideoDuration: () => (/* binding */ updateUserVideoDuration),\n/* harmony export */   updateVideoCount: () => (/* binding */ updateVideoCount),\n/* harmony export */   updateWalletBalance: () => (/* binding */ updateWalletBalance)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\n// Field names for Firestore collections\nconst FIELD_NAMES = {\n    // User fields\n    name: 'name',\n    email: 'email',\n    mobile: 'mobile',\n    referralCode: 'referralCode',\n    referredBy: 'referredBy',\n    referralBonusCredited: 'referralBonusCredited',\n    plan: 'plan',\n    planExpiry: 'planExpiry',\n    activeDays: 'activeDays',\n    joinedDate: 'joinedDate',\n    // Wallet fields\n    wallet: 'wallet',\n    // Bank details fields\n    bankAccountHolderName: 'bankAccountHolderName',\n    bankAccountNumber: 'bankAccountNumber',\n    bankIfscCode: 'bankIfscCode',\n    bankName: 'bankName',\n    bankDetailsUpdated: 'bankDetailsUpdated',\n    // Video fields\n    totalVideos: 'totalVideos',\n    todayVideos: 'todayVideos',\n    lastVideoDate: 'lastVideoDate',\n    videoDuration: 'videoDuration',\n    // Quick Video Advantage fields\n    quickVideoAdvantage: 'quickVideoAdvantage',\n    quickVideoAdvantageExpiry: 'quickVideoAdvantageExpiry',\n    quickVideoAdvantageDays: 'quickVideoAdvantageDays',\n    quickVideoAdvantageRemainingDays: 'quickVideoAdvantageRemainingDays',\n    quickVideoAdvantageSeconds: 'quickVideoAdvantageSeconds',\n    quickVideoAdvantageGrantedBy: 'quickVideoAdvantageGrantedBy',\n    quickVideoAdvantageGrantedAt: 'quickVideoAdvantageGrantedAt',\n    // Admin control fields\n    manuallySetActiveDays: 'manuallySetActiveDays',\n    lastActiveDaysUpdate: 'lastActiveDaysUpdate',\n    // Transaction fields\n    type: 'type',\n    amount: 'amount',\n    date: 'date',\n    status: 'status',\n    description: 'description',\n    userId: 'userId'\n};\n// Collection names\nconst COLLECTIONS = {\n    users: 'users',\n    transactions: 'transactions',\n    withdrawals: 'withdrawals',\n    plans: 'plans',\n    settings: 'settings',\n    notifications: 'notifications',\n    adminLeaves: 'adminLeaves',\n    userLeaves: 'userLeaves'\n};\n// Get user data\nasync function getUserData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserData:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            var _data_FIELD_NAMES_planExpiry, _data_FIELD_NAMES_joinedDate, _data_FIELD_NAMES_quickVideoAdvantageExpiry, _data_FIELD_NAMES_quickVideoAdvantageGrantedAt;\n            const data = userDoc.data();\n            // Ensure all values are properly typed\n            const result = {\n                name: String(data[FIELD_NAMES.name] || ''),\n                email: String(data[FIELD_NAMES.email] || ''),\n                mobile: String(data[FIELD_NAMES.mobile] || ''),\n                referralCode: String(data[FIELD_NAMES.referralCode] || ''),\n                referredBy: String(data[FIELD_NAMES.referredBy] || ''),\n                plan: String(data[FIELD_NAMES.plan] || 'Trial'),\n                planExpiry: ((_data_FIELD_NAMES_planExpiry = data[FIELD_NAMES.planExpiry]) === null || _data_FIELD_NAMES_planExpiry === void 0 ? void 0 : _data_FIELD_NAMES_planExpiry.toDate()) || null,\n                activeDays: Number(data[FIELD_NAMES.activeDays] || 0),\n                joinedDate: ((_data_FIELD_NAMES_joinedDate = data[FIELD_NAMES.joinedDate]) === null || _data_FIELD_NAMES_joinedDate === void 0 ? void 0 : _data_FIELD_NAMES_joinedDate.toDate()) || new Date(),\n                videoDuration: Number(data[FIELD_NAMES.videoDuration] || (data[FIELD_NAMES.plan] === 'Trial' ? 30 : 300)),\n                // Quick Video Advantage fields\n                quickVideoAdvantage: Boolean(data[FIELD_NAMES.quickVideoAdvantage] || false),\n                quickVideoAdvantageExpiry: ((_data_FIELD_NAMES_quickVideoAdvantageExpiry = data[FIELD_NAMES.quickVideoAdvantageExpiry]) === null || _data_FIELD_NAMES_quickVideoAdvantageExpiry === void 0 ? void 0 : _data_FIELD_NAMES_quickVideoAdvantageExpiry.toDate()) || null,\n                quickVideoAdvantageDays: Number(data[FIELD_NAMES.quickVideoAdvantageDays] || 0),\n                quickVideoAdvantageRemainingDays: Number(data[FIELD_NAMES.quickVideoAdvantageRemainingDays] || 0),\n                quickVideoAdvantageSeconds: Number(data[FIELD_NAMES.quickVideoAdvantageSeconds] || 30),\n                quickVideoAdvantageGrantedBy: String(data[FIELD_NAMES.quickVideoAdvantageGrantedBy] || ''),\n                quickVideoAdvantageGrantedAt: ((_data_FIELD_NAMES_quickVideoAdvantageGrantedAt = data[FIELD_NAMES.quickVideoAdvantageGrantedAt]) === null || _data_FIELD_NAMES_quickVideoAdvantageGrantedAt === void 0 ? void 0 : _data_FIELD_NAMES_quickVideoAdvantageGrantedAt.toDate()) || null\n            };\n            console.log('getUserData result:', result);\n            return result;\n        }\n        return null;\n    } catch (error) {\n        console.error('Error getting user data:', error);\n        return null // Return null instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get wallet data\nasync function getWalletData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getWalletData:', userId);\n            return {\n                wallet: 0\n            };\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const result = {\n                wallet: Number(data[FIELD_NAMES.wallet] || 0)\n            };\n            console.log('getWalletData result:', result);\n            return result;\n        }\n        return {\n            wallet: 0\n        };\n    } catch (error) {\n        console.error('Error getting wallet data:', error);\n        return {\n            wallet: 0\n        } // Return default instead of throwing\n        ;\n    }\n}\n// Get video count data\nasync function getVideoCountData(userId) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            var _data_FIELD_NAMES_lastVideoDate;\n            const data = userDoc.data();\n            const totalVideos = data[FIELD_NAMES.totalVideos] || 0;\n            let todayVideos = data[FIELD_NAMES.todayVideos] || 0;\n            const lastVideoDate = (_data_FIELD_NAMES_lastVideoDate = data[FIELD_NAMES.lastVideoDate]) === null || _data_FIELD_NAMES_lastVideoDate === void 0 ? void 0 : _data_FIELD_NAMES_lastVideoDate.toDate();\n            // Check if it's a new day\n            const today = new Date();\n            const isNewDay = !lastVideoDate || lastVideoDate.toDateString() !== today.toDateString();\n            // If it's a new day, reset todayVideos and update active days\n            if (isNewDay && todayVideos > 0) {\n                console.log(\"\\uD83D\\uDD04 Resetting daily video count for user \".concat(userId, \" (was \").concat(todayVideos, \")\"));\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                    [FIELD_NAMES.todayVideos]: 0\n                });\n                todayVideos = 0;\n                // Also update active days for accurate tracking\n                try {\n                    await updateUserActiveDays(userId);\n                } catch (error) {\n                    console.error('Error updating active days during daily reset:', error);\n                }\n                // Check if we need to run daily active days increment for all users\n                try {\n                    const lastGlobalReset = localStorage.getItem('lastGlobalActiveDaysReset');\n                    const today = new Date().toDateString();\n                    if (!lastGlobalReset || lastGlobalReset !== today) {\n                        console.log('🌅 Triggering daily active days increment for all users...');\n                        // Run in background without blocking user interaction\n                        dailyActiveDaysIncrement().then((result)=>{\n                            console.log('✅ Daily active days increment completed:', result);\n                            localStorage.setItem('lastGlobalActiveDaysReset', today);\n                        }).catch((error)=>{\n                            console.error('❌ Error in daily active days increment:', error);\n                        });\n                    }\n                } catch (error) {\n                    console.error('Error checking/running daily active days increment:', error);\n                }\n            }\n            return {\n                totalVideos,\n                todayVideos,\n                remainingVideos: Math.max(0, 50 - todayVideos)\n            };\n        }\n        return {\n            totalVideos: 0,\n            todayVideos: 0,\n            remainingVideos: 50\n        };\n    } catch (error) {\n        console.error('Error getting video count data:', error);\n        throw error;\n    }\n}\n// Update user data\nasync function updateUserData(userId, data) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), data);\n    } catch (error) {\n        console.error('Error updating user data:', error);\n        throw error;\n    }\n}\n// Add transaction\nasync function addTransaction(userId, transactionData) {\n    try {\n        const transaction = {\n            [FIELD_NAMES.userId]: userId,\n            [FIELD_NAMES.type]: transactionData.type,\n            [FIELD_NAMES.amount]: transactionData.amount,\n            [FIELD_NAMES.description]: transactionData.description,\n            [FIELD_NAMES.status]: transactionData.status || 'completed',\n            [FIELD_NAMES.date]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), transaction);\n    } catch (error) {\n        console.error('Error adding transaction:', error);\n        throw error;\n    }\n}\n// Get transactions\nasync function getTransactions(userId) {\n    let limitCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getTransactions:', userId);\n            return [];\n        }\n        // Temporary fix: Use only where clause without orderBy to avoid index requirement\n        // TODO: Create composite index in Firebase console for better performance\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.userId, '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const transactions = querySnapshot.docs.map((doc)=>{\n            var _doc_data_FIELD_NAMES_date;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                date: (_doc_data_FIELD_NAMES_date = doc.data()[FIELD_NAMES.date]) === null || _doc_data_FIELD_NAMES_date === void 0 ? void 0 : _doc_data_FIELD_NAMES_date.toDate()\n            };\n        });\n        // Sort in memory since we can't use orderBy without index\n        transactions.sort((a, b)=>{\n            const dateA = a.date || new Date(0);\n            const dateB = b.date || new Date(0);\n            return dateB.getTime() - dateA.getTime() // Descending order\n            ;\n        });\n        return transactions;\n    } catch (error) {\n        console.error('Error getting transactions:', error);\n        return [] // Return empty array instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get referrals\nasync function getReferrals(referralCode) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referredBy, '==', referralCode));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return querySnapshot.docs.map((doc)=>{\n            var _doc_data_FIELD_NAMES_joinedDate;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: (_doc_data_FIELD_NAMES_joinedDate = doc.data()[FIELD_NAMES.joinedDate]) === null || _doc_data_FIELD_NAMES_joinedDate === void 0 ? void 0 : _doc_data_FIELD_NAMES_joinedDate.toDate()\n            };\n        });\n    } catch (error) {\n        console.error('Error getting referrals:', error);\n        throw error;\n    }\n}\n// Update video count (single video)\nasync function updateVideoCount(userId) {\n    try {\n        const today = new Date();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        // Get current data to check if we need to reset daily count\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            var _data_FIELD_NAMES_lastVideoDate;\n            const data = userDoc.data();\n            const lastVideoDate = (_data_FIELD_NAMES_lastVideoDate = data[FIELD_NAMES.lastVideoDate]) === null || _data_FIELD_NAMES_lastVideoDate === void 0 ? void 0 : _data_FIELD_NAMES_lastVideoDate.toDate();\n            const currentTodayVideos = data[FIELD_NAMES.todayVideos] || 0;\n            // Check if it's a new day\n            const isNewDay = !lastVideoDate || lastVideoDate.toDateString() !== today.toDateString();\n            if (isNewDay && currentTodayVideos > 0) {\n                // Reset today's count and then increment\n                console.log(\"\\uD83D\\uDD04 Resetting and updating daily video count for user \".concat(userId));\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                    [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n                    [FIELD_NAMES.todayVideos]: 1,\n                    [FIELD_NAMES.lastVideoDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n                });\n            } else {\n                // Normal increment\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                    [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n                    [FIELD_NAMES.todayVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n                    [FIELD_NAMES.lastVideoDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n                });\n            }\n        } else {\n            // User doesn't exist, create with initial values\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n                [FIELD_NAMES.todayVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n                [FIELD_NAMES.lastVideoDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n            });\n        }\n    } catch (error) {\n        console.error('Error updating video count:', error);\n        throw error;\n    }\n}\n// Batch video submission (for submitting exactly 50 videos at once)\nasync function submitBatchVideos(userId) {\n    let videoCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n    try {\n        var _data_FIELD_NAMES_lastVideoDate;\n        if (videoCount !== 50) {\n            throw new Error(\"Invalid batch size: \".concat(videoCount, \". Expected exactly 50 videos.\"));\n        }\n        const today = new Date();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        // Get current data to validate submission\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(userRef);\n        if (!userDoc.exists()) {\n            throw new Error('User not found');\n        }\n        const data = userDoc.data();\n        const lastVideoDate = (_data_FIELD_NAMES_lastVideoDate = data[FIELD_NAMES.lastVideoDate]) === null || _data_FIELD_NAMES_lastVideoDate === void 0 ? void 0 : _data_FIELD_NAMES_lastVideoDate.toDate();\n        const currentTodayVideos = data[FIELD_NAMES.todayVideos] || 0;\n        const currentTotalVideos = data[FIELD_NAMES.totalVideos] || 0;\n        // Check if user already submitted today\n        if (currentTodayVideos >= 50) {\n            throw new Error('Daily video limit already reached. Cannot submit more videos today.');\n        }\n        // Check if it's a new day\n        const isNewDay = !lastVideoDate || lastVideoDate.toDateString() !== today.toDateString();\n        let newTodayVideos;\n        let newTotalVideos;\n        if (isNewDay && currentTodayVideos > 0) {\n            // Reset today's count and set to 50\n            console.log(\"\\uD83D\\uDD04 Resetting daily count and submitting batch for user \".concat(userId));\n            newTodayVideos = 50;\n            newTotalVideos = currentTotalVideos + 50;\n        } else {\n            // Add to existing count (should be 0 + 50 = 50 for first submission)\n            newTodayVideos = Math.min(currentTodayVideos + 50, 50) // Cap at 50\n            ;\n            newTotalVideos = currentTotalVideos + 50;\n        }\n        // Atomic update - all fields updated together\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.totalVideos]: newTotalVideos,\n            [FIELD_NAMES.todayVideos]: newTodayVideos,\n            [FIELD_NAMES.lastVideoDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n        });\n        console.log(\"✅ Batch submission successful for user \".concat(userId, \": +50 videos (Total: \").concat(newTotalVideos, \", Today: \").concat(newTodayVideos, \")\"));\n        return {\n            totalVideos: newTotalVideos,\n            todayVideos: newTodayVideos,\n            videosAdded: 50\n        };\n    } catch (error) {\n        console.error('Error submitting batch videos:', error);\n        throw error;\n    }\n}\n// Reset daily video count for a user (admin function)\nasync function resetDailyVideoCount(userId) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.todayVideos]: 0\n        });\n        console.log(\"✅ Reset daily video count for user \".concat(userId));\n    } catch (error) {\n        console.error('Error resetting daily video count:', error);\n        throw error;\n    }\n}\n// Centralized Active Days Calculation\nasync function calculateUserActiveDays(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            console.error('User data not found for active days calculation:', userId);\n            return 1;\n        }\n        const today = new Date();\n        let activeDays = 1 // Always start with 1\n        ;\n        if (userData.plan === 'Trial') {\n            // For trial users, calculate based on joined date (start from 1)\n            const joinedDate = userData.joinedDate || new Date();\n            const daysDifference = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24));\n            activeDays = Math.max(1, daysDifference + 1) // Day 0 = 1 active day, Day 1 = 2 active days, etc.\n            ;\n        } else {\n            // For paid plans, calculate from plan activation date excluding leave days\n            const planActivationDate = userData.planExpiry ? new Date(userData.planExpiry.getTime() - getPlanValidityDays(userData.plan) * 24 * 60 * 60 * 1000) : userData.joinedDate || new Date();\n            const daysSincePlanActivated = Math.floor((today.getTime() - planActivationDate.getTime()) / (1000 * 60 * 60 * 24));\n            // Get leave days count\n            const { isAdminLeaveDay, isUserOnLeave } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(app-pages-browser)/./src/lib/leaveService.ts\"));\n            let totalLeaveDays = 0;\n            // Count admin leave days since plan activation\n            for(let i = 0; i <= daysSincePlanActivated; i++){\n                const checkDate = new Date(planActivationDate.getTime() + i * 24 * 60 * 60 * 1000);\n                const isAdminLeave = await isAdminLeaveDay(checkDate);\n                const isUserLeave = await isUserOnLeave(userId, checkDate);\n                if (isAdminLeave || isUserLeave) {\n                    totalLeaveDays++;\n                }\n            }\n            // Calculate active days: Days since plan activated - leave days + 1 (for activation day)\n            activeDays = Math.max(1, daysSincePlanActivated - totalLeaveDays + 1);\n        }\n        return activeDays;\n    } catch (error) {\n        console.error('Error calculating user active days:', error);\n        return 1 // Return 1 on error to ensure minimum value\n        ;\n    }\n}\n// Update user's active days (respects manual admin settings)\nasync function updateUserActiveDays(userId) {\n    let forceUpdate = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            console.error('User data not found for active days update:', userId);\n            return 1;\n        }\n        // Check if active days were manually set by admin (skip auto-calculation unless forced)\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(userRef);\n        const userDocData = userDoc.data();\n        const manuallySetActiveDays = (userDocData === null || userDocData === void 0 ? void 0 : userDocData.manuallySetActiveDays) || false;\n        if (manuallySetActiveDays && !forceUpdate) {\n            console.log(\"⏭️ Skipping active days auto-update for user \".concat(userId, \" - manually set by admin (current: \").concat(userData.activeDays, \")\"));\n            return userData.activeDays || 1;\n        }\n        let newActiveDays;\n        if (manuallySetActiveDays && forceUpdate) {\n            // Even when forced, if manually set, don't recalculate - keep current value\n            console.log(\"⚠️ Force update requested but active days manually set for user \".concat(userId, \" - keeping current value\"));\n            newActiveDays = userData.activeDays || 1;\n        } else {\n            // Calculate correct active days using centralized function only for auto-calculated users\n            newActiveDays = await calculateUserActiveDays(userId);\n        }\n        // Only update if the value has changed\n        const currentActiveDays = userData.activeDays || 0;\n        if (newActiveDays !== currentActiveDays) {\n            console.log(\"\\uD83D\\uDCC5 Updating active days for user \".concat(userId, \": \").concat(currentActiveDays, \" → \").concat(newActiveDays));\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.activeDays]: newActiveDays\n            });\n        }\n        return newActiveDays;\n    } catch (error) {\n        console.error('Error updating user active days:', error);\n        throw error;\n    }\n}\n// Daily active days increment for all users (runs automatically every day)\nasync function dailyActiveDaysIncrement() {\n    try {\n        console.log('🌅 Starting daily active days increment...');\n        const today = new Date();\n        const todayDateString = today.toDateString();\n        // Check if today is an admin leave day\n        const { isAdminLeaveDay } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(app-pages-browser)/./src/lib/leaveService.ts\"));\n        const isAdminLeave = await isAdminLeaveDay(today);\n        if (isAdminLeave) {\n            console.log('⏸️ Skipping active days increment - Admin leave day');\n            return {\n                incrementedCount: 0,\n                skippedCount: 0,\n                errorCount: 0,\n                reason: 'Admin leave day'\n            };\n        }\n        const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users));\n        let incrementedCount = 0;\n        let skippedCount = 0;\n        let errorCount = 0;\n        for (const userDoc of usersSnapshot.docs){\n            try {\n                var _userData_FIELD_NAMES_lastActiveDaysUpdate;\n                const userData = userDoc.data();\n                const userId = userDoc.id;\n                // Check if we already updated today\n                const lastUpdate = (_userData_FIELD_NAMES_lastActiveDaysUpdate = userData[FIELD_NAMES.lastActiveDaysUpdate]) === null || _userData_FIELD_NAMES_lastActiveDaysUpdate === void 0 ? void 0 : _userData_FIELD_NAMES_lastActiveDaysUpdate.toDate();\n                if (lastUpdate && lastUpdate.toDateString() === todayDateString) {\n                    skippedCount++;\n                    continue;\n                }\n                // Check if user is on leave today\n                const { isUserOnLeave } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(app-pages-browser)/./src/lib/leaveService.ts\"));\n                const isUserLeave = await isUserOnLeave(userId, today);\n                if (isUserLeave) {\n                    console.log(\"⏸️ Skipping active days increment for user \".concat(userId, \" - User leave day\"));\n                    // Still update the last update date to mark that we processed this user today\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), {\n                        [FIELD_NAMES.lastActiveDaysUpdate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n                    });\n                    skippedCount++;\n                    continue;\n                }\n                // Check if active days were manually set by admin\n                const manuallySetActiveDays = userData[FIELD_NAMES.manuallySetActiveDays] || false;\n                let newActiveDays;\n                if (manuallySetActiveDays) {\n                    // For manually set active days, just increment by 1 from current value\n                    const currentActiveDays = userData[FIELD_NAMES.activeDays] || 1;\n                    newActiveDays = currentActiveDays + 1;\n                    console.log(\"\\uD83D\\uDCC5 Manual active days increment for user \".concat(userId, \": \").concat(currentActiveDays, \" → \").concat(newActiveDays));\n                } else {\n                    // For auto-calculated active days, use centralized calculation\n                    newActiveDays = await calculateUserActiveDays(userId);\n                    console.log(\"\\uD83D\\uDCC5 Auto-calculated active days for user \".concat(userId, \": \").concat(newActiveDays));\n                }\n                // Prepare update data\n                const updateData = {\n                    [FIELD_NAMES.activeDays]: newActiveDays,\n                    [FIELD_NAMES.lastActiveDaysUpdate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n                };\n                // Handle quick video advantage decrement\n                const currentQuickAdvantage = userData[FIELD_NAMES.quickVideoAdvantage] || false;\n                const currentRemainingDays = userData[FIELD_NAMES.quickVideoAdvantageRemainingDays] || 0;\n                if (currentQuickAdvantage && currentRemainingDays > 0) {\n                    const newRemainingDays = currentRemainingDays - 1;\n                    updateData[FIELD_NAMES.quickVideoAdvantageRemainingDays] = newRemainingDays;\n                    // If remaining days reach 0, disable quick video advantage\n                    if (newRemainingDays <= 0) {\n                        updateData[FIELD_NAMES.quickVideoAdvantage] = false;\n                        updateData[FIELD_NAMES.quickVideoAdvantageExpiry] = null;\n                        console.log(\"⏰ Quick video advantage expired for user \".concat(userId));\n                        // Add transaction record for expiry\n                        try {\n                            await addTransaction(userId, {\n                                type: 'quick_advantage_expired',\n                                amount: 0,\n                                description: 'Quick video advantage expired (time limit reached)'\n                            });\n                        } catch (transactionError) {\n                            console.error('Error adding expiry transaction:', transactionError);\n                        }\n                    } else {\n                        console.log(\"⏰ Quick video advantage for user \".concat(userId, \": \").concat(currentRemainingDays, \" → \").concat(newRemainingDays, \" days remaining\"));\n                    }\n                }\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), updateData);\n                incrementedCount++;\n                const currentActiveDays = userData[FIELD_NAMES.activeDays] || 0;\n                console.log(\"\\uD83D\\uDCC5 Updated active days for user \".concat(userId, \": \").concat(currentActiveDays, \" → \").concat(newActiveDays));\n            } catch (error) {\n                console.error(\"Error updating active days for user \".concat(userDoc.id, \":\"), error);\n                errorCount++;\n            }\n        }\n        console.log(\"✅ Daily active days increment completed: \".concat(incrementedCount, \" incremented, \").concat(skippedCount, \" skipped, \").concat(errorCount, \" errors\"));\n        return {\n            incrementedCount,\n            skippedCount,\n            errorCount\n        };\n    } catch (error) {\n        console.error('Error in daily active days increment:', error);\n        throw error;\n    }\n}\n// Migrate quick video advantage from expiry date to remaining days system\nasync function migrateQuickVideoAdvantageSystem() {\n    try {\n        console.log('🔄 Starting quick video advantage system migration...');\n        const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users));\n        let migratedCount = 0;\n        let skippedCount = 0;\n        let errorCount = 0;\n        for (const userDoc of usersSnapshot.docs){\n            try {\n                var _userData_FIELD_NAMES_quickVideoAdvantageExpiry;\n                const userData = userDoc.data();\n                const userId = userDoc.id;\n                // Skip if user doesn't have quick video advantage\n                if (!userData[FIELD_NAMES.quickVideoAdvantage]) {\n                    skippedCount++;\n                    continue;\n                }\n                // Skip if already migrated (has remainingDays field)\n                if (userData[FIELD_NAMES.quickVideoAdvantageRemainingDays] !== undefined) {\n                    skippedCount++;\n                    continue;\n                }\n                // Calculate remaining days from expiry date\n                let remainingDays = 0;\n                const expiry = (_userData_FIELD_NAMES_quickVideoAdvantageExpiry = userData[FIELD_NAMES.quickVideoAdvantageExpiry]) === null || _userData_FIELD_NAMES_quickVideoAdvantageExpiry === void 0 ? void 0 : _userData_FIELD_NAMES_quickVideoAdvantageExpiry.toDate();\n                if (expiry) {\n                    const now = new Date();\n                    const timeDiff = expiry.getTime() - now.getTime();\n                    remainingDays = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));\n                }\n                // Update user with remaining days\n                const updateData = {\n                    [FIELD_NAMES.quickVideoAdvantageRemainingDays]: remainingDays\n                };\n                // If expired, disable the advantage\n                if (remainingDays <= 0) {\n                    updateData[FIELD_NAMES.quickVideoAdvantage] = false;\n                    updateData[FIELD_NAMES.quickVideoAdvantageExpiry] = null;\n                }\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), updateData);\n                migratedCount++;\n                console.log(\"✅ Migrated user \".concat(userId, \": \").concat(remainingDays, \" days remaining\"));\n            } catch (error) {\n                console.error(\"Error migrating user \".concat(userDoc.id, \":\"), error);\n                errorCount++;\n            }\n        }\n        console.log(\"✅ Quick video advantage migration completed: \".concat(migratedCount, \" migrated, \").concat(skippedCount, \" skipped, \").concat(errorCount, \" errors\"));\n        return {\n            migratedCount,\n            skippedCount,\n            errorCount\n        };\n    } catch (error) {\n        console.error('Error migrating quick video advantage system:', error);\n        throw error;\n    }\n}\n// Fix all users' active days (admin function)\nasync function fixAllUsersActiveDays() {\n    try {\n        console.log('🔧 Starting to fix all users active days...');\n        const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users));\n        let fixedCount = 0;\n        let errorCount = 0;\n        for (const userDoc of usersSnapshot.docs){\n            try {\n                await updateUserActiveDays(userDoc.id, true) // Force update\n                ;\n                fixedCount++;\n            } catch (error) {\n                console.error(\"Error fixing active days for user \".concat(userDoc.id, \":\"), error);\n                errorCount++;\n            }\n        }\n        console.log(\"✅ Fixed active days for \".concat(fixedCount, \" users, \").concat(errorCount, \" errors\"));\n        return {\n            fixedCount,\n            errorCount\n        };\n    } catch (error) {\n        console.error('Error fixing all users active days:', error);\n        throw error;\n    }\n}\n// Recalculate active days for all users using centralized calculation (admin function)\nasync function recalculateAllUsersActiveDays() {\n    try {\n        console.log('🔄 Starting to recalculate all users active days with centralized formula...');\n        const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users));\n        let recalculatedCount = 0;\n        let errorCount = 0;\n        for (const userDoc of usersSnapshot.docs){\n            try {\n                const userData = userDoc.data();\n                const userId = userDoc.id;\n                // Use centralized calculation for correct active days\n                const correctActiveDays = await calculateUserActiveDays(userId);\n                // Update only if different from current value\n                const currentActiveDays = userData.activeDays || 0;\n                if (correctActiveDays !== currentActiveDays) {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), {\n                        [FIELD_NAMES.activeDays]: correctActiveDays,\n                        [FIELD_NAMES.manuallySetActiveDays]: false // Reset manual flag\n                    });\n                    console.log(\"\\uD83D\\uDCC5 Recalculated active days for user \".concat(userId, \": \").concat(currentActiveDays, \" → \").concat(correctActiveDays));\n                    recalculatedCount++;\n                }\n            } catch (error) {\n                console.error(\"Error recalculating active days for user \".concat(userDoc.id, \":\"), error);\n                errorCount++;\n            }\n        }\n        console.log(\"✅ Recalculated active days for \".concat(recalculatedCount, \" users, \").concat(errorCount, \" errors\"));\n        return {\n            recalculatedCount,\n            errorCount\n        };\n    } catch (error) {\n        console.error('Error recalculating all users active days:', error);\n        throw error;\n    }\n}\n// Update wallet balance\nasync function updateWalletBalance(userId, amount) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.wallet]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(amount)\n        });\n    } catch (error) {\n        console.error('Error updating wallet balance:', error);\n        throw error;\n    }\n}\n// Save bank details\nasync function saveBankDetails(userId, bankDetails) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            throw new Error('Invalid userId provided');\n        }\n        // Validate bank details\n        validateBankDetails(bankDetails);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.bankAccountHolderName]: bankDetails.accountHolderName.trim(),\n            [FIELD_NAMES.bankAccountNumber]: bankDetails.accountNumber.trim(),\n            [FIELD_NAMES.bankIfscCode]: bankDetails.ifscCode.trim().toUpperCase(),\n            [FIELD_NAMES.bankName]: bankDetails.bankName.trim(),\n            [FIELD_NAMES.bankDetailsUpdated]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        });\n        console.log('Bank details saved successfully for user:', userId);\n    } catch (error) {\n        console.error('Error saving bank details:', error);\n        throw error;\n    }\n}\n// Get bank details\nasync function getBankDetails(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getBankDetails:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            // Check if bank details exist\n            if (data[FIELD_NAMES.bankAccountNumber]) {\n                const result = {\n                    accountHolderName: String(data[FIELD_NAMES.bankAccountHolderName] || ''),\n                    accountNumber: String(data[FIELD_NAMES.bankAccountNumber] || ''),\n                    ifscCode: String(data[FIELD_NAMES.bankIfscCode] || ''),\n                    bankName: String(data[FIELD_NAMES.bankName] || '')\n                };\n                console.log('getBankDetails result found');\n                return result;\n            }\n        }\n        console.log('No bank details found for user');\n        return null;\n    } catch (error) {\n        console.error('Error getting bank details:', error);\n        return null;\n    }\n}\n// Get plan-based earning amount (per batch of 50 videos)\nfunction getPlanEarning(plan) {\n    const planEarnings = {\n        'Trial': 10,\n        'Starter': 25,\n        'Basic': 75,\n        'Premium': 150,\n        'Gold': 200,\n        'Platinum': 250,\n        'Diamond': 400\n    };\n    return planEarnings[plan] || 10 // Default to trial earning (per batch of 50 videos)\n    ;\n}\n// Get plan-based video duration (in seconds)\nfunction getPlanVideoDuration(plan) {\n    const planDurations = {\n        'Trial': 30,\n        'Starter': 300,\n        'Basic': 300,\n        'Premium': 300,\n        'Gold': 180,\n        'Platinum': 120,\n        'Diamond': 60 // 1 minute (Rs 9999 plan)\n    };\n    return planDurations[plan] || 30 // Default to trial duration (30 seconds)\n    ;\n}\n// Get plan validity duration in days\nfunction getPlanValidityDays(plan) {\n    const planValidityDays = {\n        'Trial': 2,\n        'Starter': 30,\n        'Basic': 30,\n        'Premium': 30,\n        'Gold': 30,\n        'Platinum': 30,\n        'Diamond': 30,\n        '499': 30,\n        '1499': 30,\n        '2999': 30,\n        '3999': 30,\n        '5999': 30,\n        '9999': 30 // Legacy plan mapping\n    };\n    return planValidityDays[plan] || 2 // Default to trial duration (2 days)\n    ;\n}\n// Check if user's plan is expired based on active days and plan validity\nasync function isUserPlanExpired(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                expired: true,\n                reason: 'User data not found'\n            };\n        }\n        // Use centralized calculation for active days\n        const activeDays = await calculateUserActiveDays(userId);\n        // If user is on Trial plan, check expiry based on active days\n        if (userData.plan === 'Trial') {\n            // Trial expires when active days > 2 (i.e., on day 3)\n            const trialDaysLeft = Math.max(0, 2 - activeDays);\n            return {\n                expired: activeDays > 2,\n                reason: activeDays > 2 ? 'Trial period expired' : undefined,\n                daysLeft: trialDaysLeft,\n                activeDays: activeDays\n            };\n        }\n        // For paid plans, check if planExpiry is set\n        if (userData.planExpiry) {\n            const today = new Date();\n            const expired = today > userData.planExpiry;\n            const daysLeft = expired ? 0 : Math.ceil((userData.planExpiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            return {\n                expired,\n                reason: expired ? 'Plan subscription expired' : undefined,\n                daysLeft,\n                activeDays: activeDays\n            };\n        }\n        // If planExpiry is not set, calculate based on active days and plan validity\n        const planValidityDays = getPlanValidityDays(userData.plan);\n        const daysLeft = Math.max(0, planValidityDays - activeDays);\n        const expired = activeDays > planValidityDays // Expires when active days exceed plan validity\n        ;\n        return {\n            expired,\n            reason: expired ? \"Plan validity period (\".concat(planValidityDays, \" days) exceeded based on active days\") : undefined,\n            daysLeft,\n            activeDays: activeDays\n        };\n    } catch (error) {\n        console.error('Error checking plan expiry:', error);\n        return {\n            expired: true,\n            reason: 'Error checking plan status'\n        };\n    }\n}\n// Update user's plan expiry when admin changes plan\nasync function updateUserPlanExpiry(userId, newPlan, customExpiryDate) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        if (newPlan === 'Trial') {\n            // Trial plan doesn't have expiry, it's based on joined date\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: null\n            });\n        } else {\n            // Set expiry date for paid plans\n            let expiryDate;\n            if (customExpiryDate) {\n                expiryDate = customExpiryDate;\n            } else {\n                // Calculate expiry based on plan validity\n                const validityDays = getPlanValidityDays(newPlan);\n                const today = new Date();\n                expiryDate = new Date(today.getTime() + validityDays * 24 * 60 * 60 * 1000);\n            }\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiryDate)\n            });\n            console.log(\"Updated plan expiry for user \".concat(userId, \" to \").concat(expiryDate.toDateString()));\n        }\n    } catch (error) {\n        console.error('Error updating plan expiry:', error);\n        throw error;\n    }\n}\n// Get referral bonus based on plan\nfunction getReferralBonus(plan) {\n    const referralBonuses = {\n        'Trial': 0,\n        '499': 50,\n        '1499': 150,\n        '2999': 300,\n        '3999': 400,\n        '5999': 700,\n        '9999': 1200,\n        'Starter': 50,\n        'Basic': 150,\n        'Premium': 300,\n        'Gold': 400,\n        'Platinum': 700,\n        'Diamond': 1200\n    };\n    return referralBonuses[plan] || 0;\n}\n// Process referral bonus when admin upgrades user from Trial to paid plan\nasync function processReferralBonus(userId, oldPlan, newPlan) {\n    try {\n        // Only process bonus when upgrading FROM Trial TO a paid plan\n        if (oldPlan !== 'Trial' || newPlan === 'Trial') {\n            console.log('Referral bonus only applies when upgrading from Trial to paid plan');\n            return;\n        }\n        console.log(\"Processing referral bonus for user \".concat(userId, \" upgrading from \").concat(oldPlan, \" to \").concat(newPlan));\n        // Get the user's data to find their referral info\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (!userDoc.exists()) {\n            console.log('User not found');\n            return;\n        }\n        const userData = userDoc.data();\n        const referredBy = userData[FIELD_NAMES.referredBy];\n        const alreadyCredited = userData[FIELD_NAMES.referralBonusCredited];\n        if (!referredBy) {\n            console.log('User was not referred by anyone, skipping bonus processing');\n            return;\n        }\n        if (alreadyCredited) {\n            console.log('Referral bonus already credited for this user, skipping');\n            return;\n        }\n        console.log('Finding referrer with code:', referredBy);\n        // Find the referrer by referral code\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', referredBy), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        if (querySnapshot.empty) {\n            console.log('Referral code not found:', referredBy);\n            return;\n        }\n        const referrerDoc = querySnapshot.docs[0];\n        const referrerId = referrerDoc.id;\n        const bonusAmount = getReferralBonus(newPlan);\n        console.log(\"Found referrer: \".concat(referrerId, \", bonus amount: ₹\").concat(bonusAmount));\n        if (bonusAmount > 0) {\n            // Add bonus to referrer's wallet\n            await updateWalletBalance(referrerId, bonusAmount);\n            // Add 50 videos to referrer's total video count\n            const referrerRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, referrerId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(referrerRef, {\n                [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(50)\n            });\n            // Mark referral bonus as credited for this user\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.referralBonusCredited]: true\n            });\n            // Add transaction record for referral bonus\n            await addTransaction(referrerId, {\n                type: 'referral_bonus',\n                amount: bonusAmount,\n                description: \"Referral bonus for \".concat(newPlan, \" plan upgrade + 50 bonus videos (User: \").concat(userData[FIELD_NAMES.name], \")\")\n            });\n            console.log(\"✅ Referral bonus processed: ₹\".concat(bonusAmount, \" + 50 videos for referrer \").concat(referrerId));\n        } else {\n            console.log('No bonus amount calculated, skipping');\n        }\n    } catch (error) {\n        console.error('❌ Error processing referral bonus:', error);\n    // Don't throw error to avoid breaking plan update\n    }\n}\n// Get user video settings (duration and earning per batch)\nasync function getUserVideoSettings(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                videoDuration: 30,\n                earningPerBatch: 10,\n                plan: 'Trial',\n                hasQuickAdvantage: false\n            };\n        }\n        // Check if user has active quick video advantage\n        const hasActiveQuickAdvantage = checkQuickVideoAdvantageActive(userData);\n        let videoDuration = userData.videoDuration;\n        // If user has active quick video advantage, use custom seconds or default to 30\n        if (hasActiveQuickAdvantage) {\n            videoDuration = userData.quickVideoAdvantageSeconds || 30 // Use custom duration or default to 30 seconds\n            ;\n        } else {\n            // Use plan-based video duration, but allow admin overrides for non-trial users\n            if (!videoDuration || userData.plan === 'Trial') {\n                videoDuration = getPlanVideoDuration(userData.plan);\n            }\n        }\n        return {\n            videoDuration: videoDuration,\n            earningPerBatch: getPlanEarning(userData.plan),\n            plan: userData.plan,\n            hasQuickAdvantage: hasActiveQuickAdvantage,\n            quickAdvantageExpiry: userData.quickVideoAdvantageExpiry\n        };\n    } catch (error) {\n        console.error('Error getting user video settings:', error);\n        return {\n            videoDuration: 30,\n            earningPerBatch: 10,\n            plan: 'Trial',\n            hasQuickAdvantage: false\n        };\n    }\n}\n// Check if user has active quick video advantage\nfunction checkQuickVideoAdvantageActive(userData) {\n    if (!userData.quickVideoAdvantage) {\n        return false;\n    }\n    // Use remaining days if available (new system), otherwise fall back to expiry date (legacy)\n    if (userData.quickVideoAdvantageRemainingDays !== undefined) {\n        return userData.quickVideoAdvantageRemainingDays > 0;\n    }\n    // Legacy fallback for existing users\n    if (userData.quickVideoAdvantageExpiry) {\n        const now = new Date();\n        return now < userData.quickVideoAdvantageExpiry;\n    }\n    return false;\n}\n// Grant quick video advantage to user (admin function)\nasync function grantQuickVideoAdvantage(userId, days, grantedBy) {\n    let seconds = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 30;\n    try {\n        if (days <= 0 || days > 365) {\n            throw new Error('Days must be between 1 and 365');\n        }\n        if (seconds < 1 || seconds > 420) {\n            throw new Error('Seconds must be between 1 and 420 (7 minutes)');\n        }\n        const now = new Date();\n        const expiry = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: true,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiry),\n            [FIELD_NAMES.quickVideoAdvantageDays]: days,\n            [FIELD_NAMES.quickVideoAdvantageRemainingDays]: days,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: seconds,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: grantedBy,\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(now)\n        });\n        console.log(\"Granted quick video advantage to user \".concat(userId, \" for \").concat(days, \" days until \").concat(expiry.toDateString()));\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_granted',\n            amount: 0,\n            description: \"Quick video advantage granted for \".concat(days, \" days by \").concat(grantedBy)\n        });\n        return {\n            success: true,\n            expiry\n        };\n    } catch (error) {\n        console.error('Error granting quick video advantage:', error);\n        throw error;\n    }\n}\n// Remove quick video advantage from user (admin function)\nasync function removeQuickVideoAdvantage(userId, removedBy) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: false,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: null,\n            [FIELD_NAMES.quickVideoAdvantageDays]: 0,\n            [FIELD_NAMES.quickVideoAdvantageRemainingDays]: 0,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: 30,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: '',\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: null\n        });\n        console.log(\"Removed quick video advantage from user \".concat(userId));\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_removed',\n            amount: 0,\n            description: \"Quick video advantage removed by \".concat(removedBy)\n        });\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Error removing quick video advantage:', error);\n        throw error;\n    }\n}\n// Update user video duration (admin function)\nasync function updateUserVideoDuration(userId, durationInSeconds) {\n    try {\n        // Validate duration (quick durations: 1, 10, 30 seconds OR standard durations: 1-7 minutes)\n        const isQuickDuration = [\n            1,\n            10,\n            30\n        ].includes(durationInSeconds);\n        const isStandardDuration = durationInSeconds >= 60 && durationInSeconds <= 420;\n        if (!isQuickDuration && !isStandardDuration) {\n            throw new Error('Video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration');\n        }\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.videoDuration]: durationInSeconds\n        });\n        console.log(\"Updated video duration for user \".concat(userId, \" to \").concat(durationInSeconds, \" seconds\"));\n    } catch (error) {\n        console.error('Error updating user video duration:', error);\n        throw error;\n    }\n}\n// Validate bank details\nfunction validateBankDetails(bankDetails) {\n    const { accountHolderName, accountNumber, ifscCode, bankName } = bankDetails;\n    if (!accountHolderName || accountHolderName.trim().length < 2) {\n        throw new Error('Account holder name must be at least 2 characters long');\n    }\n    if (!accountNumber || !/^\\d{9,18}$/.test(accountNumber.trim())) {\n        throw new Error('Account number must be 9-18 digits');\n    }\n    if (!ifscCode || !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(ifscCode.trim().toUpperCase())) {\n        throw new Error('Invalid IFSC code format (e.g., SBIN0001234)');\n    }\n    if (!bankName || bankName.trim().length < 2) {\n        throw new Error('Bank name must be at least 2 characters long');\n    }\n}\n// Add notification (admin function) - All notifications are now blocking\nasync function addNotification(notification) {\n    try {\n        const notificationData = {\n            title: notification.title,\n            message: notification.message,\n            type: notification.type,\n            targetUsers: notification.targetUsers,\n            userIds: notification.userIds || [],\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdBy: notification.createdBy\n        };\n        console.log('Adding notification to Firestore:', notificationData);\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), notificationData);\n        console.log('Notification added successfully with ID:', docRef.id);\n        // Verify the notification was added\n        const addedDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(docRef);\n        if (addedDoc.exists()) {\n            console.log('Notification verified in database:', addedDoc.data());\n        } else {\n            console.warn('Notification not found after adding');\n        }\n        return docRef.id;\n    } catch (error) {\n        console.error('Error adding notification:', error);\n        throw error;\n    }\n}\n// Get notifications for a user\nasync function getUserNotifications(userId) {\n    let limitCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserNotifications:', userId);\n            return [];\n        }\n        console.log(\"Loading notifications for user: \".concat(userId));\n        // Try to get notifications with fallback for indexing issues\n        let allUsersSnapshot, specificUserSnapshot;\n        try {\n            // Get notifications targeted to all users\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n            console.log(\"Found \".concat(allUsersSnapshot.docs.length, \" notifications for all users\"));\n        } catch (error) {\n            console.warn('Error querying all users notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n        }\n        try {\n            // Get notifications targeted to specific user\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n            console.log(\"Found \".concat(specificUserSnapshot.docs.length, \" notifications for specific user\"));\n        } catch (error) {\n            console.warn('Error querying specific user notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n        }\n        const notifications = [];\n        // Process all users notifications\n        allUsersSnapshot.docs.forEach((doc)=>{\n            var _doc_data_createdAt;\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: ((_doc_data_createdAt = doc.data().createdAt) === null || _doc_data_createdAt === void 0 ? void 0 : _doc_data_createdAt.toDate()) || new Date()\n            });\n        });\n        // Process specific user notifications\n        specificUserSnapshot.docs.forEach((doc)=>{\n            var _doc_data_createdAt;\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: ((_doc_data_createdAt = doc.data().createdAt) === null || _doc_data_createdAt === void 0 ? void 0 : _doc_data_createdAt.toDate()) || new Date()\n            });\n        });\n        // Sort by creation date (newest first)\n        notifications.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime());\n        const finalNotifications = notifications.slice(0, limitCount);\n        console.log(\"Returning \".concat(finalNotifications.length, \" total notifications for user\"));\n        return finalNotifications;\n    } catch (error) {\n        console.error('Error getting user notifications:', error);\n        return [];\n    }\n}\n// Get all notifications (admin function)\nasync function getAllNotifications() {\n    let limitCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50;\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const notifications = querySnapshot.docs.map((doc)=>{\n            var _doc_data_createdAt;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                createdAt: ((_doc_data_createdAt = doc.data().createdAt) === null || _doc_data_createdAt === void 0 ? void 0 : _doc_data_createdAt.toDate()) || new Date()\n            };\n        });\n        return notifications;\n    } catch (error) {\n        console.error('Error getting all notifications:', error);\n        return [];\n    }\n}\n// Delete notification (admin function)\nasync function deleteNotification(notificationId) {\n    try {\n        if (!notificationId || typeof notificationId !== 'string') {\n            throw new Error('Invalid notification ID provided');\n        }\n        console.log('Deleting notification:', notificationId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications, notificationId));\n        console.log('Notification deleted successfully');\n    } catch (error) {\n        console.error('Error deleting notification:', error);\n        throw error;\n    }\n}\n// Mark notification as read\nasync function markNotificationAsRead(notificationId, userId) {\n    try {\n        // For now, we'll store read status in localStorage since it's user-specific\n        const readNotifications = JSON.parse(localStorage.getItem(\"read_notifications_\".concat(userId)) || '[]');\n        if (!readNotifications.includes(notificationId)) {\n            readNotifications.push(notificationId);\n            localStorage.setItem(\"read_notifications_\".concat(userId), JSON.stringify(readNotifications));\n        }\n    } catch (error) {\n        console.error('Error marking notification as read:', error);\n    }\n}\n// Check if notification is read\nfunction isNotificationRead(notificationId, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(\"read_notifications_\".concat(userId)) || '[]');\n        return readNotifications.includes(notificationId);\n    } catch (error) {\n        console.error('Error checking notification read status:', error);\n        return false;\n    }\n}\n// Get unread notification count\nfunction getUnreadNotificationCount(notifications, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(\"read_notifications_\".concat(userId)) || '[]');\n        return notifications.filter((notification)=>!readNotifications.includes(notification.id)).length;\n    } catch (error) {\n        console.error('Error getting unread notification count:', error);\n        return 0;\n    }\n}\n// Get unread notifications - All notifications are now blocking\nasync function getUnreadNotifications(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUnreadNotifications:', userId);\n            return [];\n        }\n        console.log(\"Loading unread notifications for user: \".concat(userId));\n        // Get all notifications for the user\n        const allNotifications = await getUserNotifications(userId, 50);\n        // Filter for unread notifications\n        const readNotifications = JSON.parse(localStorage.getItem(\"read_notifications_\".concat(userId)) || '[]');\n        const unreadNotifications = allNotifications.filter((notification)=>notification.id && !readNotifications.includes(notification.id));\n        console.log(\"Found \".concat(unreadNotifications.length, \" unread notifications\"));\n        return unreadNotifications;\n    } catch (error) {\n        console.error('Error getting unread notifications:', error);\n        return [];\n    }\n}\n// Check if user has unread notifications\nasync function hasUnreadNotifications(userId) {\n    try {\n        const unreadNotifications = await getUnreadNotifications(userId);\n        return unreadNotifications.length > 0;\n    } catch (error) {\n        console.error('Error checking for unread notifications:', error);\n        return false;\n    }\n}\n// Check if user has pending withdrawals\nasync function hasPendingWithdrawals(userId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('status', '==', 'pending'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return !snapshot.empty;\n    } catch (error) {\n        console.error('Error checking pending withdrawals:', error);\n        return false;\n    }\n}\n// Check if withdrawal is allowed (timing, leave restrictions, and plan restrictions)\nasync function checkWithdrawalAllowed(userId) {\n    try {\n        // Check user plan first\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                allowed: false,\n                reason: 'Unable to verify user information. Please try again.'\n            };\n        }\n        // Check if user is on trial plan\n        if (userData.plan === 'Trial') {\n            return {\n                allowed: false,\n                reason: 'Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals.'\n            };\n        }\n        // Check if user has pending withdrawals\n        const hasPending = await hasPendingWithdrawals(userId);\n        if (hasPending) {\n            return {\n                allowed: false,\n                reason: 'You have a pending withdrawal request. Please wait for it to be processed before submitting a new request.'\n            };\n        }\n        const now = new Date();\n        const currentHour = now.getHours();\n        // Check time restrictions (10 AM to 6 PM)\n        if (currentHour < 10 || currentHour >= 18) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are only allowed between 10:00 AM to 6:00 PM'\n            };\n        }\n        // Check admin leave day\n        const { isAdminLeaveDay } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(app-pages-browser)/./src/lib/leaveService.ts\"));\n        const isAdminLeave = await isAdminLeaveDay(now);\n        if (isAdminLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on admin leave/holiday days'\n            };\n        }\n        // Check user leave day\n        const { isUserOnLeave } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(app-pages-browser)/./src/lib/leaveService.ts\"));\n        const isUserLeave = await isUserOnLeave(userId, now);\n        if (isUserLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on your leave days'\n            };\n        }\n        return {\n            allowed: true\n        };\n    } catch (error) {\n        console.error('Error checking withdrawal allowed:', error);\n        return {\n            allowed: false,\n            reason: 'Unable to verify withdrawal eligibility. Please try again.'\n        };\n    }\n}\n// Create withdrawal request\nasync function createWithdrawalRequest(userId, amount, bankDetails) {\n    try {\n        // Check minimum withdrawal amount\n        if (amount < 50) {\n            throw new Error('Minimum withdrawal amount is ₹50');\n        }\n        // Check if withdrawal is allowed\n        const withdrawalCheck = await checkWithdrawalAllowed(userId);\n        if (!withdrawalCheck.allowed) {\n            throw new Error(withdrawalCheck.reason);\n        }\n        // Check if user has sufficient balance\n        const walletData = await getWalletData(userId);\n        if (walletData.wallet < amount) {\n            throw new Error('Insufficient wallet balance');\n        }\n        // Debit the amount from user's wallet immediately\n        await updateWalletBalance(userId, -amount);\n        // Add transaction record for withdrawal debit\n        await addTransaction(userId, {\n            type: 'withdrawal_request',\n            amount: -amount,\n            description: \"Withdrawal request submitted - ₹\".concat(amount, \" debited from wallet\")\n        });\n        const withdrawalData = {\n            userId,\n            amount,\n            bankDetails,\n            status: 'pending',\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), withdrawalData);\n        return docRef.id;\n    } catch (error) {\n        console.error('Error creating withdrawal request:', error);\n        throw error;\n    }\n}\n// Get user withdrawals\nasync function getUserWithdrawals(userId) {\n    let limitCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return snapshot.docs.map((doc)=>{\n            var _doc_data_date;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                date: (_doc_data_date = doc.data().date) === null || _doc_data_date === void 0 ? void 0 : _doc_data_date.toDate()\n            };\n        });\n    } catch (error) {\n        console.error('Error getting user withdrawals:', error);\n        return [];\n    }\n}\n// Generate unique referral code with MYN prefix and sequential numbering\nasync function generateUniqueReferralCode() {\n    try {\n        // Try to get count from server for sequential numbering\n        try {\n            const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getCountFromServer)(usersCollection);\n            const count = snapshot.data().count;\n            const sequentialNumber = (count + 1).toString().padStart(4, '0');\n            return \"MYN\".concat(sequentialNumber);\n        } catch (countError) {\n            console.warn('Failed to get count from server, using fallback method:', countError);\n            // Fallback to timestamp-based generation\n            const timestamp = Date.now().toString().slice(-4);\n            const randomPart = Math.random().toString(36).substring(2, 4).toUpperCase();\n            return \"MYN\".concat(timestamp).concat(randomPart);\n        }\n    } catch (error) {\n        console.error('Error generating unique referral code:', error);\n        // Final fallback\n        const timestamp = Date.now().toString().slice(-4);\n        return \"MYN\".concat(timestamp);\n    }\n}\n// Generate sequential referral code (alias for generateUniqueReferralCode)\nasync function generateSequentialReferralCode() {\n    return generateUniqueReferralCode();\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/dataService.ts\n"));

/***/ })

});