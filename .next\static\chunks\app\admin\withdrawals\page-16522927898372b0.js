(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[581],{3571:(e,t,a)=>{Promise.resolve().then(a.bind(a,4128))},3737:(e,t,a)=>{"use strict";function s(e,t,a){if(!e||0===e.length)return void alert("No data to export");let s=a||Object.keys(e[0]),r=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],n=new Blob(["\uFEFF"+[s.join(","),...e.map(e=>s.map(t=>{let a=e[t];if(null==a)return"";let s=r.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return'"'.concat(e,'"')}return a instanceof Date?'"'.concat(a.toLocaleDateString(),'"'):"object"==typeof a&&null!==a&&a.toDate?'"'.concat(a.toDate().toLocaleDateString(),'"'):s&&("number"==typeof a||!isNaN(Number(a)))?'"'.concat(a,'"'):"number"==typeof a?a.toString():'"'.concat(String(a),'"')}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),l=document.createElement("a");if(void 0!==l.download){let e=URL.createObjectURL(n);l.setAttribute("href",e),l.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),l.style.visibility="hidden",document.body.appendChild(l),l.click(),document.body.removeChild(l)}}function r(e){return e.map(e=>({"User ID":e.id||"",Name:e.name||"",Email:e.email||"",Mobile:String(e.mobile||""),"Referral Code":e.referralCode||"","Referred By":e.referredBy||"Direct",Plan:e.plan||"","Plan Expiry":e.planExpiry instanceof Date?e.planExpiry.toLocaleDateString():e.planExpiry?new Date(e.planExpiry).toLocaleDateString():"","Active Days":e.activeDays||0,"Total Videos":e.totalVideos||0,"Today Videos":e.todayVideos||0,"Last Video Date":e.lastVideoDate instanceof Date?e.lastVideoDate.toLocaleDateString():e.lastVideoDate?new Date(e.lastVideoDate).toLocaleDateString():"","Video Duration (seconds)":e.videoDuration||300,"Quick Video Advantage":e.quickVideoAdvantage?"Yes":"No","Quick Video Advantage Expiry":e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():e.quickVideoAdvantageExpiry?new Date(e.quickVideoAdvantageExpiry).toLocaleDateString():"","Quick Video Advantage Days":e.quickVideoAdvantageDays||"","Quick Video Advantage Granted By":e.quickVideoAdvantageGrantedBy||"","Wallet Balance":e.wallet||0,"Referral Bonus Credited":e.referralBonusCredited?"Yes":"No",Status:e.status||"","Joined Date":e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():e.joinedDate?new Date(e.joinedDate).toLocaleDateString():"","Joined Time":e.joinedDate instanceof Date?e.joinedDate.toLocaleTimeString():e.joinedDate?new Date(e.joinedDate).toLocaleTimeString():""}))}function n(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function l(e){return e.map(e=>{var t,a,s,r;return{"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":String((null==(s=e.bankDetails)?void 0:s.accountNumber)||""),"IFSC Code":(null==(r=e.bankDetails)?void 0:r.ifscCode)||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}})}function i(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>s,Fz:()=>r,Pe:()=>i,dB:()=>l,sL:()=>n})},4128:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var s=a(5155),r=a(2115),n=a(6874),l=a.n(n),i=a(6681),c=a(3737),o=a(3592),d=a(4752),x=a.n(d);function u(){let{user:e,loading:t,isAdmin:n}=(0,i.wC)(),[d,u]=(0,r.useState)([]),[m,p]=(0,r.useState)(!0),[h,g]=(0,r.useState)(""),[f,b]=(0,r.useState)(""),[j,y]=(0,r.useState)(null),[N,w]=(0,r.useState)(!1),[v,D]=(0,r.useState)([]),[k,C]=(0,r.useState)(!1),[S,A]=(0,r.useState)(""),[B,L]=(0,r.useState)(!1),[E,P]=(0,r.useState)(null),[T,q]=(0,r.useState)(!0);(0,r.useEffect)(()=>{n&&V(!0)},[n]);let V=async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];try{p(!0);let{getWithdrawals:t}=await a.e(6779).then(a.bind(a,6779)),{withdrawals:s,lastDoc:r,hasMore:n}=await t(20,e?null:E),l=[];for(let e of s)try{if(!e.userId||!e.amount){console.warn("Withdrawal missing required fields:",e);continue}let t=await (0,o.getUserData)(e.userId),s=await (0,o.getWalletData)(e.userId);if(t){let{calculateUserActiveDays:r}=await Promise.resolve().then(a.bind(a,3592)),n=await r(e.userId);l.push({id:e.id,userId:e.userId,userName:t.name,userEmail:t.email,userMobile:t.mobile||"",userPlan:t.plan,userActiveDays:n,walletBalance:(null==s?void 0:s.wallet)||0,amount:e.amount,bankDetails:e.bankDetails||{accountHolderName:"",accountNumber:"",ifscCode:"",bankName:""},requestDate:e.date||new Date,status:e.status||"pending",adminNotes:e.adminNotes})}}catch(t){console.error("Error loading user data for withdrawal ".concat(e.id,":"),t)}e?u(l):u(e=>[...e,...l]),P(r),q(n)}catch(t){console.error("Error loading withdrawals:",t),e&&u([]),x().fire({icon:"error",title:"Error",text:"Failed to load withdrawals. Please try again."})}finally{p(!1)}},U=async(e,t,s)=>{try{let{updateWithdrawalStatus:r}=await a.e(6779).then(a.bind(a,6779));await r(e,t,s),u(a=>a.map(a=>a.id===e?{...a,status:t,adminNotes:s}:a)),x().fire({icon:"success",title:"Status Updated",text:"Withdrawal has been ".concat(t,"."),timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error updating withdrawal status:",e),x().fire({icon:"error",title:"Update Failed",text:"Failed to update withdrawal status. Please try again."})}},F=e=>{x().fire({title:"Approve Withdrawal",text:"Approve withdrawal of ₹".concat(e.amount," for ").concat(e.userName,"?"),icon:"question",showCancelButton:!0,confirmButtonColor:"#10b981",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Approve",cancelButtonText:"Cancel"}).then(t=>{t.isConfirmed&&U(e.id,"approved")})},I=e=>{x().fire({title:"Reject Withdrawal",text:"Please provide a reason for rejection:",input:"textarea",inputPlaceholder:"Enter rejection reason...",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Reject",cancelButtonText:"Cancel",inputValidator:e=>{if(!e)return"Please provide a reason for rejection"}}).then(t=>{t.isConfirmed&&U(e.id,"rejected",t.value)})},M=e=>{x().fire({title:"Mark as Completed",text:"Mark withdrawal of ₹".concat(e.amount," as completed?"),icon:"question",showCancelButton:!0,confirmButtonColor:"#3b82f6",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Complete",cancelButtonText:"Cancel"}).then(t=>{t.isConfirmed&&U(e.id,"completed")})},R=e=>{v.includes(e)?(D(t=>t.filter(t=>t!==e)),C(!1)):(D(t=>[...t,e]),v.length+1===H.length&&C(!0))},W=async()=>{let e;if(0===v.length)return void x().fire({icon:"warning",title:"No Selection",text:"Please select at least one withdrawal to update."});if(!S)return void x().fire({icon:"warning",title:"No Action Selected",text:"Please select an action to perform."});let t="approved"===S?"approve":"rejected"===S?"reject":"completed"===S?"mark as completed":S;if((e="rejected"===S?await x().fire({title:"Bulk ".concat(t.charAt(0).toUpperCase()+t.slice(1)),text:"Please provide a reason for rejection:",input:"textarea",inputPlaceholder:"Enter rejection reason...",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"".concat(t.charAt(0).toUpperCase()+t.slice(1)," ").concat(v.length," withdrawals"),cancelButtonText:"Cancel",inputValidator:e=>{if(!e)return"Please provide a reason for rejection"}}):await x().fire({title:"Bulk ".concat(t.charAt(0).toUpperCase()+t.slice(1)),text:"Are you sure you want to ".concat(t," ").concat(v.length," selected withdrawals?"),icon:"question",showCancelButton:!0,confirmButtonColor:"approved"===S?"#10b981":"#3b82f6",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, ".concat(t.charAt(0).toUpperCase()+t.slice(1)),cancelButtonText:"Cancel"})).isConfirmed)try{for(let t of(L(!0),v))await U(t,S,e.value);D([]),C(!1),A(""),x().fire({icon:"success",title:"Bulk Update Complete",text:"Successfully ".concat(t,"ed ").concat(v.length," withdrawals."),timer:3e3,showConfirmButton:!1})}catch(e){console.error("Error in bulk update:",e),x().fire({icon:"error",title:"Bulk Update Failed",text:"Some withdrawals could not be updated. Please try again."})}finally{L(!1)}},H=d.filter(e=>{let t=!h||e.status===h,a=!f||String(e.userName||"").toLowerCase().includes(f.toLowerCase())||String(e.userEmail||"").toLowerCase().includes(f.toLowerCase())||String(e.userMobile||"").toLowerCase().includes(f.toLowerCase());return t&&a}),_=e=>null==e||isNaN(e)?"₹0.00":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}).format(e),O=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"completed":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},Y=e=>{switch(e.toLowerCase()){case"trial":default:return"bg-gray-100 text-gray-800";case"starter":return"bg-green-100 text-green-800";case"premium":return"bg-blue-100 text-blue-800";case"gold":return"bg-yellow-100 text-yellow-800";case"platinum":return"bg-purple-100 text-purple-800";case"diamond":return"bg-pink-100 text-pink-800"}};return t||m?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading withdrawals..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(l(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Withdrawal Requests"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-gray-700",children:["Total: ",H.length,v.length>0&&(0,s.jsxs)("span",{className:"ml-2 text-blue-600 font-medium",children:["(",v.length," selected)"]})]}),(0,s.jsxs)("button",{onClick:()=>{if(0===H.length)return void x().fire({icon:"warning",title:"No Data",text:"No withdrawals to export."});let e=(0,c.dB)(H);(0,c.Bf)(e,"withdrawals"),x().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(H.length," withdrawals to CSV file."),timer:2e3,showConfirmButton:!1})},className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,s.jsxs)("button",{onClick:()=>V(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),(0,s.jsx)("input",{type:"text",value:f,onChange:e=>b(e.target.value),placeholder:"Search user name, email, or mobile...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,s.jsxs)("select",{value:h,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"All Status"}),(0,s.jsx)("option",{value:"pending",children:"Pending"}),(0,s.jsx)("option",{value:"approved",children:"Approved"}),(0,s.jsx)("option",{value:"rejected",children:"Rejected"}),(0,s.jsx)("option",{value:"completed",children:"Completed"})]})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)("button",{onClick:()=>{g(""),b("")},className:"w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:"Clear Filters"})})]})}),v.length>0&&(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mx-6 mb-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-blue-800 font-medium",children:[(0,s.jsx)("i",{className:"fas fa-check-square mr-2"}),v.length," withdrawal",v.length>1?"s":""," selected"]}),(0,s.jsxs)("select",{value:S,onChange:e=>A(e.target.value),className:"px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"Select Action"}),(0,s.jsx)("option",{value:"approved",children:"Approve Selected"}),(0,s.jsx)("option",{value:"rejected",children:"Reject Selected"}),(0,s.jsx)("option",{value:"completed",children:"Mark as Completed"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:W,disabled:!S||B,className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:B?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Processing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-bolt mr-2"}),"Apply Action"]})}),(0,s.jsxs)("button",{onClick:()=>{D([]),C(!1),A("")},className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-times mr-2"}),"Clear Selection"]})]})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:k,onChange:()=>{k?(D([]),C(!1)):(D(H.map(e=>e.id)),C(!0))},className:"mr-2"}),"Select All"]})}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Mobile"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Days"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet Balance"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Account Holder"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Bank Name"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Account Number"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"IFSC Code"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Request Date"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:H.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("input",{type:"checkbox",checked:v.includes(e.id),onChange:()=>R(e.id),className:"rounded"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.userEmail})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900",children:e.userMobile||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(Y(e.userPlan)),children:e.userPlan})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[e.userActiveDays," days"]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900",children:_(e.walletBalance)})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-lg font-bold text-green-600",children:_(e.amount)})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900",children:e.bankDetails.accountHolderName||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900",children:e.bankDetails.bankName||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900 font-mono",children:e.bankDetails.accountNumber||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900 font-mono",children:e.bankDetails.ifscCode||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.requestDate.toLocaleDateString()}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(O(e.status)),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[(0,s.jsx)("button",{onClick:()=>{y(e),w(!0)},className:"text-blue-600 hover:text-blue-900",children:"View"}),"pending"===e.status&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{onClick:()=>F(e),className:"text-green-600 hover:text-green-900",children:"Approve"}),(0,s.jsx)("button",{onClick:()=>I(e),className:"text-red-600 hover:text-red-900",children:"Reject"})]}),"approved"===e.status&&(0,s.jsx)("button",{onClick:()=>M(e),className:"text-blue-600 hover:text-blue-900",children:"Complete"})]})]},e.id))})]})}),T&&(0,s.jsx)("div",{className:"bg-white px-4 py-3 border-t border-gray-200 text-center",children:(0,s.jsx)("button",{onClick:()=>{T&&!m&&V(!1)},disabled:m,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:opacity-50",children:m?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Loading..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Load More Withdrawals"]})})}),(0,s.jsxs)("div",{className:"bg-white px-4 py-3 border-t border-gray-200 text-center text-sm text-gray-600",children:["Showing ",d.length," withdrawals",!T&&d.length>0&&" (All loaded)"]})]})}),N&&j&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-bold",children:"Withdrawal Details"}),(0,s.jsx)("button",{onClick:()=>w(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-times"})})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"User"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:j.userName}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:j.userEmail})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Mobile Number"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:j.userMobile||"N/A"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Plan"}),(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(Y(j.userPlan)),children:j.userPlan})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Active Days"}),(0,s.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[j.userActiveDays," days"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Wallet Balance"}),(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:_(j.walletBalance)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Withdrawal Amount"}),(0,s.jsx)("p",{className:"text-lg font-bold text-green-600",children:_(j.amount)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Bank Details"}),(0,s.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Account Holder:"})," ",j.bankDetails.accountHolderName]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Bank:"})," ",j.bankDetails.bankName]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Account Number:"})," ",j.bankDetails.accountNumber]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"IFSC Code:"})," ",j.bankDetails.ifscCode]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(O(j.status)),children:j.status.charAt(0).toUpperCase()+j.status.slice(1)})]}),j.adminNotes&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Admin Notes"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:j.adminNotes})]})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,6681,8441,1684,7358],()=>t(3571)),_N_E=e.O()}]);