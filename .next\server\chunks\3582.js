exports.id=3582,exports.ids=[3582],exports.modules={3582:()=>{throw Error("Module parse failed: Identifier 'getLiveActiveDays' has already been declared (459:22)\nFile was processed with these loaders:\n * ./node_modules/next/dist/build/webpack/loaders/next-flight-client-module-loader.js\n * ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js\nYou may need an additional loader to handle the result of these loaders.\n| }\n| // Get live active days for user display (always returns current calculation)\n> export async function getLiveActiveDays(userId) {\n|     try {\n|         console.log(`\uD83D\uDD0D Getting live active days for user ${userId}`);")}};